#!/usr/bin/env node

/**
 * Example: How to Generate High-Quality SEO Blog Posts
 *
 * This example demonstrates the proper way to use the blog generation system
 * with real DataForSEO integration and quality controls.
 */

import * as dotenv from "dotenv";
import { dataForSEO } from "../src/utils/dataforseo.js";
import chalk from "chalk";

// Load environment variables
dotenv.config();

// Example topics that work well with the system
const GOOD_TOPICS = [
  "AI Customer Service Automation",
  "Multi-Agent AI Systems for Business",
  "Workflow Automation with Artificial Intelligence",
  "Building Custom AI Agents",
  "AI-Powered Sales Automation"
];

// Topics to avoid (too broad or ambiguous)
const BAD_TOPICS = [
  "AI agents schedule meetings doctor", // Too specific and weird
  "AI technology",                      // Too broad
  "Best AI",                           // Too vague
  "AI near me"                         // Location-based (avoid)
];

async function main() {
  console.log(chalk.bold.cyan("\n🚀 Blog Generation Best Practices\n"));

  // Example 1: Interactive mode with keyword review (RECOMMENDED)
  console.log(chalk.yellow("Example 1: Interactive Generation with Keyword Review"));
  console.log(chalk.dim("This is the recommended approach for high-quality content\n"));

  console.log(chalk.green("Command:"));
  console.log("npm run generate:blog:interactive\n");

  console.log(chalk.dim("What happens:"));
  console.log("1. You choose topic, category, and languages interactively");
  console.log("2. System performs keyword research with DataForSEO");
  console.log("3. AI filters out irrelevant keywords");
  console.log("4. YOU select primary and secondary keywords");
  console.log("5. Content is generated focusing on YOUR selected keywords\n");

  // Example 2: Direct generation with review
  console.log(chalk.yellow("Example 2: Direct Generation with Keyword Review"));
  console.log(chalk.green("Command:"));
  console.log(`npm run generate:blog -- --topic "${GOOD_TOPICS[0]}" --category ai-agents --review-keywords\n`);

  // Example 3: Multi-language generation
  console.log(chalk.yellow("Example 3: Multi-Language Generation"));
  console.log(chalk.green("Command:"));
  console.log(`npm run generate:blog -- --topic "${GOOD_TOPICS[1]}" --all-languages --single-research --review-keywords\n`);

  console.log(chalk.dim("Benefits:"));
  console.log("- Uses English keyword research for all languages (saves API calls)");
  console.log("- Generates consistent content across languages");
  console.log("- You review keywords once, applied to all languages\n");

  // Example 4: Programmatic usage
  console.log(chalk.yellow("Example 4: Programmatic Usage"));
  console.log(chalk.green("Code:"));
  console.log(`
import { dataForSEO } from './src/utils/dataforseo';

// Generate with keyword review
const imageUrl = await dataForSEO.generateBlogPost(
  "AI Customer Service Automation",
  "en",
  "ai-agents",
  undefined,  // researchLanguage
  undefined,  // sharedImageUrl
  true        // reviewKeywords - forces interactive selection
);
`);

  // Best practices
  console.log(chalk.cyan("\n📚 Best Practices:\n"));

  console.log(chalk.green("✅ DO:"));
  console.log("- Use specific, focused topics");
  console.log("- Always review keywords before generation");
  console.log("- Choose keywords with good search volume AND relevance");
  console.log("- Use the category that best matches your topic");
  console.log("- Run QA checks after generation: npm run qa:blog\n");

  console.log(chalk.red("❌ DON'T:"));
  console.log("- Use vague or overly broad topics");
  console.log("- Include locations unless specifically needed");
  console.log("- Mix unrelated concepts in one topic");
  console.log("- Skip keyword review (you'll get irrelevant content)");
  console.log("- Accept keywords just because they have high volume\n");

  // Quality checklist
  console.log(chalk.cyan("🔍 Quality Checklist:\n"));
  console.log("When selecting keywords, ask yourself:");
  console.log("1. Is this keyword directly related to my topic?");
  console.log("2. Would my target audience search for this?");
  console.log("3. Can I create valuable content around this keyword?");
  console.log("4. Does the search intent match my content goals?");
  console.log("5. Is the difficulty reasonable for my domain authority?\n");

  // Example keyword selection
  console.log(chalk.cyan("📊 Example Keyword Selection:\n"));
  console.log("Topic: 'AI Customer Service Automation'\n");

  console.log(chalk.green("Good keyword choices:"));
  console.log("✓ ai customer service (2,400 vol, 0.42 diff) - Perfect match");
  console.log("✓ automated customer support (1,900 vol, 0.38 diff) - Relevant variant");
  console.log("✓ ai chatbot customer service (1,100 vol, 0.45 diff) - Specific feature\n");

  console.log(chalk.red("Bad keyword choices:"));
  console.log("✗ customer service (165,000 vol, 0.78 diff) - Too broad, not AI-specific");
  console.log("✗ ai technology (8,100 vol, 0.65 diff) - Too generic");
  console.log("✗ doctor appointment ai (320 vol, 0.15 diff) - Different industry\n");

  // Demonstrate actual generation (commented out to avoid accidental runs)
  console.log(chalk.yellow("Ready to generate? Uncomment the code below:\n"));

  /*
  try {
    // Example: Generate a blog post with review
    const topic = "AI Customer Service Automation";
    const language = "en";
    const category = "ai-agents";

    console.log(chalk.cyan(`Generating blog post about: ${topic}`));

    const imageUrl = await dataForSEO.generateBlogPost(
      topic,
      language,
      category,
      undefined,
      undefined,
      true  // Force keyword review
    );

    console.log(chalk.green("\n✅ Blog post generated successfully!"));
    console.log(`📁 Location: src/content/blog/${language}/`);
    console.log(`🖼️  Featured image: ${imageUrl}`);

  } catch (error) {
    console.error(chalk.red("Generation failed:"), error.message);
  }
  */

  console.log(chalk.bold.green("\n✨ Happy blogging with real SEO data!\n"));
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error(chalk.red("Error:"), error);
    process.exit(1);
  });
}
