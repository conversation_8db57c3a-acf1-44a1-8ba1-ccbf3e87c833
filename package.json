{"name": "agentik-ai-website", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "deploy": "npm run build && echo _worker.js > dist/.assetsignore && wrangler deploy", "db:create": "wrangler d1 create agentik-contacts", "db:schema": "wrangler d1 execute agentik-contacts --file=./schema.sql", "db:query": "wrangler d1 execute agentik-contacts --command", "kv:create": "wrangler kv:namespace create CACHE", "setup": "powershell -ExecutionPolicy Bypass -File setup-cloudflare.ps1", "astro": "astro", "generate:blog": "npx tsx scripts/generate-blog-post.ts", "generate:blog:all": "npx tsx scripts/generate-blog-post.ts --all-languages", "generate:blog:interactive": "npx tsx scripts/generate-blog-post.ts --interactive", "test:dataforseo": "npx tsx scripts/test-dataforseo.ts", "qa:blog": "npx tsx scripts/qa-blog-generation.ts", "example:blog": "npx tsx scripts/example-generate-blog.ts"}, "dependencies": {"@astrojs/cloudflare": "^12.6.0", "@astrojs/tailwind": "^5.0.0", "astro": "^5.10.0", "dataforseo-client": "^2.0.1", "tailwindcss": "^3.0.0"}, "devDependencies": {"@astrojs/check": "^0.3.0", "@types/node": "^20.10.0", "chalk": "^5.3.0", "commander": "^11.1.0", "dotenv": "^16.5.0", "openai": "^5.6.0", "ora": "^8.2.0", "prompts": "^2.4.2", "tsx": "^4.6.2", "typescript": "^5.0.0"}}