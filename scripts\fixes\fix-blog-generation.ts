#!/usr/bin/env node

/**
 * Test script to verify the research language parameter is being passed correctly
 * to the DataForSEO generateBlogPost method.
 */

import { dataForSEO } from "../../src/utils/dataforseo";
import chalk from "chalk";

async function testResearchLanguageParameter() {
  console.log(chalk.cyan("\n🔍 Testing Research Language Parameter\n"));

  // Test scenarios
  const testCases = [
    {
      name: "English content with English research",
      topic: "AI customer service",
      language: "en" as const,
      researchLanguage: "en" as const,
      expected: "Should use English keyword research",
    },
    {
      name: "Spanish content with English research",
      topic: "AI customer service",
      language: "es" as const,
      researchLanguage: "en" as const,
      expected: "Should use English keyword research (not ask for Spanish keywords)",
    },
    {
      name: "Italian content with English research",
      topic: "AI customer service",
      language: "it" as const,
      researchLanguage: "en" as const,
      expected: "Should use English keyword research (not ask for Italian keywords)",
    },
    {
      name: "Spanish content with undefined research language",
      topic: "AI customer service",
      language: "es" as const,
      researchLanguage: undefined,
      expected: "Should ask for Spanish keywords",
    },
  ];

  for (const testCase of testCases) {
    console.log(chalk.yellow(`\nTest: ${testCase.name}`));
    console.log(chalk.dim(`Expected: ${testCase.expected}`));
    console.log(chalk.dim(`Parameters:`));
    console.log(chalk.dim(`  - topic: ${testCase.topic}`));
    console.log(chalk.dim(`  - language: ${testCase.language}`));
    console.log(chalk.dim(`  - researchLanguage: ${testCase.researchLanguage || "undefined"}`));

    try {
      // Create a spy to track what language is passed to performKeywordResearch
      const originalGenerateBlogPost = dataForSEO.generateBlogPost.bind(dataForSEO);
      let capturedResearchLang: string | undefined;

      // Override the generateBlogPost to capture what's happening
      dataForSEO.generateBlogPost = async function (
        topic: string,
        language: "en" | "es" | "it" = "en",
        category: string = "ai-agents",
        researchLanguage?: "en" | "es" | "it",
        sharedImageUrl?: string,
        reviewKeywords: boolean = false,
      ) {
        console.log(chalk.green(`\n✓ generateBlogPost called with:`));
        console.log(chalk.dim(`  - topic: ${topic}`));
        console.log(chalk.dim(`  - language: ${language}`));
        console.log(chalk.dim(`  - researchLanguage: ${researchLanguage || "undefined"}`));

        capturedResearchLang = researchLanguage;

        // Don't actually generate the blog post, just test the parameter passing
        console.log(chalk.cyan(`\n📊 Would perform keyword research in: ${researchLanguage || language}`));

        return undefined;
      };

      // Call the function with test parameters
      await dataForSEO.generateBlogPost(
        testCase.topic,
        testCase.language,
        "ai-agents",
        testCase.researchLanguage,
        undefined,
        false
      );

      // Restore original function
      dataForSEO.generateBlogPost = originalGenerateBlogPost;

      // Verify
      if (testCase.researchLanguage && capturedResearchLang !== testCase.researchLanguage) {
        console.log(chalk.red(`\n❌ Failed: Research language not passed correctly`));
        console.log(chalk.red(`   Expected: ${testCase.researchLanguage}`));
        console.log(chalk.red(`   Received: ${capturedResearchLang || "undefined"}`));
      } else {
        console.log(chalk.green(`\n✅ Passed: Research language parameter handled correctly`));
      }

    } catch (error) {
      console.log(chalk.red(`\n❌ Error: ${error}`));
    }

    console.log(chalk.dim("\n" + "─".repeat(80)));
  }
}

// Run the test
testResearchLanguageParameter().catch(console.error);
