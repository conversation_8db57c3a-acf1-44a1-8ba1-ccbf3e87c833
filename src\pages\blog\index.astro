---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
import { getCollection } from 'astro:content';
import { getLangFromAstro, useTranslations, localizePath } from '../../i18n/utils';

// Get the current language (should be 'it' for this page)
const lang = getLangFromAstro(Astro);
const t = useTranslations(lang);

// Get all Italian blog posts
const allPosts = await getCollection('blog', ({ data, id }) => {
  return data.language === 'it' && data.draft !== true;
});

// Sort posts by date (newest first)
const posts = allPosts.sort(
  (a, b) => b.data.publishDate.valueOf() - a.data.publishDate.valueOf()
);

// Get unique categories
const categories = [...new Set(posts.map(post => post.data.category))];

// Pagination
const postsPerPage = 9;
const currentPage = 1;
const totalPages = Math.ceil(posts.length / postsPerPage);
const paginatedPosts = posts.slice(0, postsPerPage);

// Featured posts (latest 3)
const featuredPosts = posts.slice(0, 3);
---

<Layout
  title="Blog - AI Agenti e Automazione Aziendale | Agentik AI"
  description="Scopri le ultime novità su agenti AI, automazione aziendale, casi di studio e best practice per trasformare il tuo business con l'intelligenza artificiale."
>
  <Header currentPage="blog" />

  <main class="blog-listing">
    <!-- Hero Section -->
    <section class="blog-hero">
      <div class="container">
        <h1>Blog & <span class="gradient-text">Risorse</span></h1>
        <p class="subtitle">
          Esplora guide approfondite, casi di studio e insights sul mondo degli agenti AI
          e dell'automazione aziendale intelligente.
        </p>

        <!-- Categories Filter -->
        <div class="categories-filter">
          <button class="category-btn active" data-category="all">
            Tutti gli articoli
          </button>
          {categories.map(category => (
            <button class="category-btn" data-category={category}>
              {t(`blog.category.${category}`)}
            </button>
          ))}
        </div>
      </div>
    </section>

    <!-- Featured Posts -->
    <section class="featured-section">
      <div class="container">
        <h2>Articoli in <span class="gradient-text">Evidenza</span></h2>
        <div class="featured-grid">
          {featuredPosts.map((post, index) => {
            const formattedDate = new Date(post.data.publishDate).toLocaleDateString('it', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            });

            return (
              <article class={`featured-card ${index === 0 ? 'featured-main' : ''}`}>
                {post.data.featuredImage && (
                  <div class="featured-image">
                    <img
                      src={post.data.featuredImage.src}
                      alt={post.data.featuredImage.alt}
                      loading="lazy"
                    />
                  </div>
                )}
                <div class="featured-content">
                  <div class="post-meta">
                    <span class="category">{post.data.category}</span>
                    <span class="reading-time">{post.data.readingTime} min</span>
                  </div>
                  <h3>
                    <a href={`/blog/${post.id}`}>
                      {post.data.title}
                    </a>
                  </h3>
                  <p class="excerpt">{post.data.metaDescription}</p>
                  <div class="post-footer">
                    <time datetime={post.data.publishDate.toISOString()}>
                      {formattedDate}
                    </time>
                    <a href={`/blog/${post.id}`} class="read-more">
                      Leggi di più →
                    </a>
                  </div>
                </div>
              </article>
            );
          })}
        </div>
      </div>
    </section>

    <!-- All Posts Grid -->
    <section class="posts-section">
      <div class="container">
        <h2>Tutti gli <span class="gradient-text">Articoli</span></h2>
        <div class="posts-grid" id="posts-grid">
          {paginatedPosts.map(post => {
            const formattedDate = new Date(post.data.publishDate).toLocaleDateString('it', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            });

            return (
              <article class="post-card" data-category={post.data.category}>
                {post.data.featuredImage && (
                  <div class="post-image">
                    <img
                      src={post.data.featuredImage.src}
                      alt={post.data.featuredImage.alt}
                      loading="lazy"
                    />
                  </div>
                )}
                <div class="post-content">
                  <div class="post-meta">
                    <span class="category">{post.data.category}</span>
                    <time datetime={post.data.publishDate.toISOString()}>
                      {formattedDate}
                    </time>
                  </div>
                  <h3>
                    <a href={`/blog/${post.id}`}>
                      {post.data.title}
                    </a>
                  </h3>
                  <p class="excerpt">{post.data.metaDescription}</p>
                  <div class="post-tags">
                    {post.data.tags.slice(0, 3).map(tag => (
                      <span class="tag">{tag}</span>
                    ))}
                  </div>
                </div>
              </article>
            );
          })}
        </div>

        <!-- Pagination -->
        {totalPages > 1 && (
          <nav class="pagination">
            <button class="page-btn" disabled>← Precedente</button>
            <div class="page-numbers">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button class={`page-num ${page === currentPage ? 'active' : ''}`}>
                  {page}
                </button>
              ))}
            </div>
            <button class="page-btn">Successivo →</button>
          </nav>
        )}
      </div>
    </section>



    <!-- Bottom CTA -->
    <section class="bottom-cta">
      <div class="container">
        <h2>Pronto a Trasformare il Tuo Business con l'AI?</h2>
        <p>
          Scopri come i nostri agenti AI personalizzati possono automatizzare
          i tuoi processi e liberare il potenziale del tuo team.
        </p>
        <a href={localizePath("/contact", lang)} class="cta-button">
          Richiedi una Consulenza Gratuita
          <span>→</span>
        </a>
      </div>
    </section>
  </main>

  <Footer />

  <script>
    // Category filtering
    document.addEventListener('DOMContentLoaded', function() {
      const categoryBtns = document.querySelectorAll('.category-btn');
      const posts = document.querySelectorAll('.post-card');

      categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const category = this.dataset.category;

          // Update active button
          categoryBtns.forEach(b => b.classList.remove('active'));
          this.classList.add('active');

          // Filter posts
          posts.forEach(post => {
            if (category === 'all' || (post as HTMLElement).dataset.category === category) {
              (post as HTMLElement).style.display = 'block';
            } else {
              (post as HTMLElement).style.display = 'none';
            }
          });
        });
      });
    });
  </script>
</Layout>

<style>
  .blog-listing {
    min-height: 100vh;
    background: #0a0a0f;
  }

  /* Hero Section */
  .blog-hero {
    padding: 140px 0 80px;
    text-align: center;
    background: linear-gradient(180deg, rgba(139, 92, 246, 0.1) 0%, transparent 100%);
  }

  .blog-hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 20px;
  }

  .subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto 40px;
    line-height: 1.6;
  }

  .gradient-text {
    background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Categories Filter */
  .categories-filter {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .category-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    padding: 10px 24px;
    border-radius: 30px;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .category-btn:hover {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.3);
    color: #ffffff;
  }

  .category-btn.active {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
    color: #8b5cf6;
  }

  /* Featured Section */
  .featured-section {
    padding: 80px 0;
  }

  .featured-section h2 {
    font-size: 2.5rem;
    margin-bottom: 50px;
    text-align: center;
  }

  .featured-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
  }

  .featured-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .featured-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
  }

  .featured-main {
    grid-column: 1;
    grid-row: span 2;
  }

  .featured-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
  }

  .featured-main .featured-image {
    height: 400px;
  }

  .featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .featured-card:hover .featured-image img {
    transform: scale(1.05);
  }

  .featured-content {
    padding: 30px;
  }

  .post-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
  }

  .category {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 13px;
    text-transform: capitalize;
  }

  .reading-time {
    color: rgba(255, 255, 255, 0.5);
    font-size: 13px;
  }

  .featured-content h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
  }

  .featured-main .featured-content h3 {
    font-size: 2rem;
  }

  .featured-content h3 a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .featured-content h3 a:hover {
    color: #8b5cf6;
  }

  .excerpt {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 20px;
  }

  .post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .post-footer time {
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
  }

  .read-more {
    color: #8b5cf6;
    text-decoration: none;
    font-weight: 500;
    transition: transform 0.3s ease;
    display: inline-block;
  }

  .read-more:hover {
    transform: translateX(5px);
  }

  /* Posts Grid */
  .posts-section {
    padding: 80px 0;
    background: rgba(255, 255, 255, 0.01);
  }

  .posts-section h2 {
    font-size: 2.5rem;
    margin-bottom: 50px;
    text-align: center;
  }

  .posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
  }

  .post-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
  }

  .post-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
  }

  .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .post-card:hover .post-image img {
    transform: scale(1.05);
  }

  .post-content {
    padding: 25px;
  }

  .post-content h3 {
    font-size: 1.25rem;
    margin-bottom: 12px;
  }

  .post-content h3 a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .post-content h3 a:hover {
    color: #8b5cf6;
  }

  .post-tags {
    display: flex;
    gap: 8px;
    margin-top: 15px;
  }

  .tag {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.6);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
  }

  /* Pagination */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
  }

  .page-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .page-btn:hover:not(:disabled) {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.3);
    color: #ffffff;
  }

  .page-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .page-numbers {
    display: flex;
    gap: 10px;
  }

  .page-num {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .page-num:hover {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.3);
    color: #ffffff;
  }

  .page-num.active {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
    color: #8b5cf6;
  }

  /* Newsletter Section */
  .newsletter-section {
    padding: 80px 0;
  }

  .newsletter-card {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 60px;
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
  }

  .newsletter-card h2 {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  .newsletter-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
    font-size: 1.1rem;
  }

  .cta-actions {
    display: flex;
    gap: 15px;
    max-width: 500px;
  }

  .cta-button {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    text-decoration: none;
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    border-radius: 100px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
  }

  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
  }

  .privacy-note {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
  }

  /* Bottom CTA */
  .bottom-cta {
    padding: 100px 0;
    text-align: center;
    background: linear-gradient(180deg, transparent 0%, rgba(139, 92, 246, 0.05) 100%);
  }

  .bottom-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }

  .bottom-cta p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto 40px;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .featured-grid {
      grid-template-columns: 1fr;
    }

    .featured-main {
      grid-column: 1;
      grid-row: 1;
    }

    .posts-grid {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .blog-hero h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1.1rem;
    }

    .categories-filter {
      justify-content: flex-start;
      overflow-x: auto;
      padding-bottom: 10px;
    }

    .featured-section h2,
    .posts-section h2 {
      font-size: 1.8rem;
    }

    .newsletter-card {
      padding: 40px 20px;
    }

    .cta-actions {
      justify-content: center;
    }

    .bottom-cta h2 {
      font-size: 1.8rem;
    }

    .bottom-cta p {
      font-size: 1rem;
    }
  }
</style>
