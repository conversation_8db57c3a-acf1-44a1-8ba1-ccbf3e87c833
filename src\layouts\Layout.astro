---
import { getLangFromAstro } from '../i18n/utils';

export interface Props {
    title: string;
    description?: string;
}

const { title, description = "Proxy42 Inc | IA Autonoma per il Business" } =
    Astro.props;
const lang = getLangFromAstro(Astro);
---

<!doctype html>
<html lang={lang}>
    <head>
        <meta charset="UTF-8" />
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <meta name="generator" content={Astro.generator} />

        <!-- Google Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600;700;800&family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
            rel="stylesheet"
        />

        <title>{title}</title>
    </head>
    <body>
        <slot />

        <script>
            // Mobile Menu Toggle Functions
            function toggleMobileMenu() {
                const hamburger = document.querySelector(".hamburger");
                const mobileNav = document.querySelector(".nav-mobile");
                const overlay = document.querySelector(".nav-overlay");

                if (hamburger) hamburger.classList.toggle("active");
                if (mobileNav) mobileNav.classList.toggle("active");
                if (overlay) overlay.classList.toggle("active");

                // Prevent body scroll when menu is open
                if (mobileNav && mobileNav.classList.contains("active")) {
                    document.body.style.overflow = "hidden";
                } else {
                    document.body.style.overflow = "auto";
                }
            }

            function closeMobileMenu() {
                const hamburger = document.querySelector(".hamburger");
                const mobileNav = document.querySelector(".nav-mobile");
                const overlay = document.querySelector(".nav-overlay");

                if (hamburger) hamburger.classList.remove("active");
                if (mobileNav) mobileNav.classList.remove("active");
                if (overlay) overlay.classList.remove("active");
                document.body.style.overflow = "auto";
            }



            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
                anchor.addEventListener("click", function (e) {
                    e.preventDefault();
                    const target = document.querySelector(
                        this.getAttribute("href"),
                    );
                    if (target) {
                        target.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                        });
                    }
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener("click", function (e) {
                const hamburger = document.querySelector(".hamburger");
                const mobileNav = document.querySelector(".nav-mobile");
                const languageSelector =
                    document.querySelector(".language-selector");

                if (
                    hamburger &&
                    mobileNav &&
                    e.target &&
                    !hamburger.contains(e.target as Node) &&
                    !mobileNav.contains(e.target as Node)
                ) {
                    closeMobileMenu();
                }

                // Close language menu when clicking outside
                if (
                    languageSelector &&
                    e.target &&
                    !languageSelector.contains(e.target as Node)
                ) {
                    languageSelector.classList.remove("active");
                }
            });

            // Close mobile menu on window resize
            window.addEventListener("resize", function () {
                if (window.innerWidth > 900) {
                    closeMobileMenu();
                }
            });

            // Header scroll effect
            const header = document.querySelector(".header");

            window.addEventListener("scroll", function () {
                let scrollTop =
                    window.pageYOffset || document.documentElement.scrollTop;

                // Add/remove scrolled class for styling
                if (header) {
                    if (scrollTop > 50) {
                        header.classList.add("scrolled");
                    } else {
                        header.classList.remove("scrolled");
                    }
                }
            });

            // Make functions globally available
            (window as any).toggleMobileMenu = toggleMobileMenu;
            (window as any).closeMobileMenu = closeMobileMenu;

            // Analytics tracking automatico
            async function trackPageView() {
                try {
                    await fetch("/api/analytics", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            page: window.location.pathname,
                            referrer: document.referrer,
                            timestamp: new Date().toISOString(),
                        }),
                    });
                } catch (error) {
                    console.log("Analytics tracking failed:", error);
                }
            }

            // Traccia la visita quando la pagina è caricata
            trackPageView();
        </script>
    </body>
</html>

<style is:global>
    /* ------------------- */
    /* --- CSS Reset --- */
    /* ------------------- */
    *,
    *::before,
    *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    html {
        scroll-behavior: smooth;
        overflow-x: hidden;
    }

    body {
        font-family: '"Work Sans"', sans-serif;
        background-color: #0b0b0f;
        color: #e0e0e0;
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
        padding-top: 80px;
        overflow-x: hidden;
        /* Space for fixed header */
    }

    img,
    picture,
    video,
    canvas,
    svg {
        display: block;
        max-width: 100%;
    }

    h1,
    h2,
    h3 {
        font-family: "Plus Jakarta Sans", "Work Sans", sans-serif;
        line-height: 1.2;
        color: #ffffff;
    }

    h1 {
        font-weight: bold;
    }

    h2,
    h3 {
        font-weight: 600;
    }

    /* ------------------- */
    /* --- Global Variables & Styles --- */
    /* ------------------- */
    :root {
        --accent-color: #4a55ff;
        --accent-hover: #6a71ff;
        --bg-color: #0b0b0f;
        --surface-color-transparent: rgba(26, 26, 34, 0.5);
        --surface-color: rgb(26, 26, 34);
        --border-color: #2d2d3a;
        --text-primary: #ffffff;
        --text-secondary: #b3b5bd;
        --container-width: 1320px;
    }

    .container {
        width: 90%;
        max-width: var(--container-width);
        margin: 0 auto;
        padding: 80px 0;
    }

    .container-small {
        width: 90%;
        max-width: var(--container-width);
        margin: 0 auto;
        padding: 10px 0;
    }

    .section-title {
        font-size: 48px;
        text-align: center;
        margin-bottom: 16px;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
    }

    .section-subtitle {
        font-size: 18px;
        text-align: center;
        color: var(--text-secondary);
        max-width: 800px;
        margin: 0 auto 64px auto;
    }

    .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        background: linear-gradient(
            135deg,
            var(--accent-color) 0%,
            var(--accent-hover) 100%
        );
        color: var(--text-primary);
        padding: 16px 32px;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 20px rgba(74, 85, 255, 0.3);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .cta-button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        transition: left 0.5s;
    }

    .cta-button:hover::before {
        left: 100%;
    }

    .cta-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(74, 85, 255, 0.4);
    }

    .secondary-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background-color: var(--surface-color-transparent);
        /* Changed from transparent */
        color: var(--text-primary);
        padding: 16px 32px;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 500;
        font-size: 16px;
        border: 2px solid var(--accent-color);
        /* Changed border to accent color */
        transition: all 0.3s ease;
        position: relative;
        opacity: 1;
        /* Ensured opacity is 1 */
    }

    .secondary-button:hover {
        background-color: var(--surface-color);
        opacity: 1;
        border-color: var(--accent-hover);
        /* Changed hover border to accent-hover for differentiation */
        transform: translateY(-2px);
    }

    /* ------------------- */
    /* --- Header & Nav --- */
    /* ------------------- */
    .header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 5%;
        background-color: rgba(11, 11, 15, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid transparent;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .header.scrolled {
        background-color: rgba(11, 11, 15, 0.98);
        border-bottom: 1px solid var(--border-color);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        max-width: var(--container-width);
        margin: 0 auto;
    }

    .logo {
        font-size: 22px;
        font-weight: 700;
        color: var(--text-primary);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .logo:hover {
        color: var(--accent-color);
    }

    /* Desktop Navigation */
    .nav-desktop {
        display: flex;
        gap: 32px;
        align-items: center;
    }

    .nav-desktop a {
        color: var(--text-secondary);
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        transition: color 0.3s ease;
    }

    .nav-desktop a:hover {
        color: var(--text-primary);
    }

    .nav-desktop a.active {
        color: var(--text-primary);
        font-weight: 600;
    }

    /* Mobile Hamburger Menu */
    .hamburger {
        display: none;
        flex-direction: column;
        cursor: pointer;
        padding: 10px;
        z-index: 1001;
        border-radius: 4px;
        transition: background-color 0.3s ease;
    }

    .hamburger:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .hamburger span {
        width: 24px;
        height: 2px;
        background-color: var(--text-primary);
        margin: 3px 0;
        transition: 0.3s;
        border-radius: 2px;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    /* Mobile Navigation Menu */
    .nav-mobile {
        display: none;
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        background-color: var(--surface-color);
        border-left: 1px solid var(--border-color);
        padding: 100px 32px 32px 32px;
        transition: right 0.3s ease;
        z-index: 999;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    }

    .nav-mobile.active {
        right: 0;
    }

    .nav-mobile a {
        display: block;
        color: var(--text-primary);
        text-decoration: none;
        font-weight: 500;
        font-size: 18px;
        padding: 16px 0;
        border-bottom: 1px solid var(--border-color);
        transition: color 0.3s ease;
    }

    .nav-mobile a:hover {
        color: var(--accent-color);
    }

    .nav-mobile a.active {
        color: var(--accent-color);
        font-weight: 600;
    }

    .nav-mobile a:last-child {
        border-bottom: none;
    }

    /* Overlay */
    .nav-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }

    .nav-overlay.active {
        display: block;
    }

    /* ------------------- */
    /* --- Partners Section --- */
    /* ------------------- */
    .partners-section {
        padding: 80px 0;
        background-color: var(--surface-color);
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
    }

    .partners-title {
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        color: var(--text-secondary);
        margin-bottom: 24px;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        font-family: '"Work Sans"', sans-serif;
        text-shadow: 0px 0px 5px rgb(70, 128, 255);
        /* Added font family */
    }

    .partners-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 40px;
        align-items: center;
        opacity: 1;
        /* Changed from 0.7 to 1 */
    }

    .partner-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 15px;
        /* Adjusted padding */
        background-color: var(--surface-color);
        /* Changed background to surface-color */
        border-radius: 12px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        min-height: 70px;
        /* Adjusted min-height */
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        /* Added box-shadow */
    }

    .partner-logo:hover {
        opacity: 1;
        transform: translateY(-4px);
        border-color: var(--accent-color);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        /* Enhanced box-shadow on hover */
    }

    .partner-logo img {
        max-width: 120px;
        max-height: 40px;
        filter: grayscale(100%) brightness(0.8);
        transition: filter 0.3s ease;
    }

    .partner-logo:hover img {
        filter: grayscale(0%) brightness(1);
    }

    /* Placeholder for partner logos */
    .partner-placeholder {
        width: 120px;
        height: 40px;
        background: linear-gradient(
            135deg,
            var(--text-secondary),
            var(--border-color)
        );
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--bg-color);
        font-weight: 600;
        font-size: 12px;
        font-family: '"Work Sans"', sans-serif;
        /* Added font family */
    }

    /* ------------------- */
    /* --- Hero Section --- */
    /* ------------------- */
    .hero {
        text-align: center;
        padding-top: 120px;
        padding-bottom: 180px;
        position: relative;
        overflow: hidden;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        background:
            linear-gradient(
                180deg,
                rgba(15, 15, 20, 0.4) 0%,
                rgba(8, 8, 12, 0.98) 100%
            ),
            linear-gradient(
                0deg,
                rgba(99, 102, 241, 0.22) 0%,
                rgba(99, 102, 241, 0.08) 50%,
                transparent 100%
            ),
            linear-gradient(
                0deg,
                rgba(168, 85, 247, 0.18) 0%,
                rgba(168, 85, 247, 0.06) 70%,
                transparent 100%
            ),
            linear-gradient(
                0deg,
                rgba(59, 130, 246, 0.16) 0%,
                rgba(59, 130, 246, 0.05) 80%,
                transparent 100%
            );
        animation: taskadeBackgroundShift 15s ease-in-out infinite;
        /* Speed adjusted */
        background-size: cover, cover, cover;
        background-position: center, center, center;
        background-attachment: fixed;
    }

    /* Animated Background Layers */
    .hero::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        /* Stroke width changed to 0.5 */
        opacity: 0.4;
        pointer-events: none;
        animation: gridPulse 16s ease-in-out infinite;
        /* Speed adjusted */
    }

    .hero::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(0deg, rgba(99, 102, 241, 0.16) 0%, transparent 60%),
            linear-gradient(0deg, rgba(168, 85, 247, 0.12) 0%, transparent 80%),
            linear-gradient(0deg, rgba(59, 130, 246, 0.14) 0%, transparent 70%),
            linear-gradient(0deg, rgba(147, 197, 253, 0.09) 0%, transparent 90%);
        pointer-events: none;
        animation: taskadeOverlayShift 36s ease-in-out infinite;
        /* Speed adjusted */
    }

    /* Floating Particles */
    .hero-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    .particle {
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(74, 85, 255, 0.6);
        border-radius: 50%;
        animation: floatUp 20s linear infinite;
        /* Speed adjusted */
    }

    .particle:nth-child(2n) {
        background: rgba(255, 107, 107, 0.4);
        animation-duration: 40s;
        /* Speed adjusted */
        animation-delay: -5s;
    }

    .particle:nth-child(3n) {
        background: rgba(138, 146, 255, 0.5);
        animation-duration: 18s;
        /* Speed adjusted */
        animation-delay: -10s;
    }

    /* Glowing Orbs */
    .hero-orbs {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    .glowing-orb {
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(74, 85, 255, 0.8);
        box-shadow: 0 0 20px rgba(74, 85, 255, 0.5);
        animation: orbFloat 12s ease-in-out infinite;
        /* Speed adjusted */
    }

    .glowing-orb:nth-child(2n) {
        background: rgba(255, 107, 107, 0.6);
        box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
        animation-duration: 30s;
        /* Speed adjusted */
        animation-delay: -5s;
    }

    .glowing-orb:nth-child(3n) {
        background: rgba(138, 146, 255, 0.7);
        box-shadow: 0 0 20px rgba(138, 146, 255, 0.5);
        animation-duration: 36s;
        /* Speed adjusted */
        animation-delay: -8s;
    }

    /* Data Stream Effect */
    .hero-datastream {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    .data-line {
        position: absolute;
        width: 1px;
        height: 100px;
        background: linear-gradient(
            to bottom,
            transparent,
            rgba(74, 85, 255, 0.8),
            transparent
        );
        animation: dataFlow 10s linear infinite;
        /* Speed adjusted */
    }

    .data-line:nth-child(2n) {
        background: linear-gradient(
            to bottom,
            transparent,
            rgba(255, 107, 107, 0.6),
            transparent
        );
        animation-duration: 12s;
        /* Speed adjusted */
        animation-delay: -3s;
    }

    .data-line:nth-child(3n) {
        background: linear-gradient(
            to bottom,
            transparent,
            rgba(138, 146, 255, 0.7),
            transparent
        );
        animation-duration: 10s;
        /* Speed adjusted */
        animation-delay: -6s;
    }

    @keyframes dataFlow {
        0% {
            transform: translateY(-100px);
            opacity: 0;
        }

        10%,
        90% {
            opacity: 1;
        }

        100% {
            transform: translateY(calc(100vh + 100px));
            opacity: 0;
        }
    }

    .hero-content {
        position: relative;
        z-index: 1;
        max-width: var(--container-width);
        margin: 0 auto;
        padding: 0 5%;
    }

    .hero h1 {
        font-size: 72px;
        max-width: 900px;
        font-weight: bold !important;
        margin: 0 auto 32px auto;
        line-height: 1.1;
        font-weight: 200;
        letter-spacing: -0.02em;
        color: #ffffff;
    }

    .hero h1 .gradient-text {
        background: linear-gradient(
            45deg,
            #714dff 0%,
            #9c83ff 15%,
            #e151ff 30%,
            #ff9719 45%,
            #ff11e3 60%,
            #714dff 75%,
            #9c83ff 90%,
            #e151ff 100%
        );
        background-size: 300% 300%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: gradientShift 8s ease-in-out infinite;
        /* Speed adjusted */
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .hero h1 .gradient-text:hover {
        animation-duration: 2s;
        /* Speed adjusted */
        transform: scale(1.05);
    }

    @keyframes gradientShift {
        0%,
        100% {
            background-position: 0% 50%;
        }

        25% {
            background-position: 100% 50%;
        }

        50% {
            background-position: 50% 100%;
        }

        75% {
            background-position: 50% 0%;
        }
    }

    /* Hero Animation Keyframes */
    @keyframes gridPulse {
        0%,
        100% {
            opacity: 0.4;
            transform: scale(1);
        }

        50% {
            opacity: 0.6;
            transform: scale(1.02);
        }
    }

    @keyframes taskadeOverlayShift {
        0%,
        100% {
            background:
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.16) 0%,
                    transparent 60%
                ),
                linear-gradient(
                    0deg,
                    rgba(168, 85, 247, 0.12) 0%,
                    transparent 80%
                ),
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.14) 0%,
                    transparent 70%
                ),
                linear-gradient(
                    0deg,
                    rgba(147, 197, 253, 0.09) 0%,
                    transparent 90%
                );
            opacity: 0.85;
        }

        25% {
            background:
                linear-gradient(
                    0deg,
                    rgba(139, 92, 246, 0.19) 0%,
                    transparent 65%
                ),
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.15) 0%,
                    transparent 75%
                ),
                linear-gradient(
                    0deg,
                    rgba(147, 197, 253, 0.11) 0%,
                    transparent 85%
                ),
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.1) 0%,
                    transparent 92%
                );
            opacity: 0.9;
        }

        50% {
            background:
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.18) 0%,
                    transparent 62%
                ),
                linear-gradient(
                    0deg,
                    rgba(139, 92, 246, 0.14) 0%,
                    transparent 78%
                ),
                linear-gradient(
                    0deg,
                    rgba(168, 85, 247, 0.11) 0%,
                    transparent 88%
                ),
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.09) 0%,
                    transparent 94%
                );
            opacity: 0.87;
        }

        75% {
            background:
                linear-gradient(
                    0deg,
                    rgba(147, 197, 253, 0.16) 0%,
                    transparent 68%
                ),
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.13) 0%,
                    transparent 82%
                ),
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.11) 0%,
                    transparent 86%
                ),
                linear-gradient(
                    0deg,
                    rgba(139, 92, 246, 0.09) 0%,
                    transparent 95%
                );
            opacity: 0.9;
        }
    }

    @keyframes floatUp {
        /* Speed adjusted */
        0% {
            transform: translateY(100vh) translateX(0px) rotate(0deg);
            opacity: 0;
        }

        10% {
            opacity: 1;
        }

        90% {
            opacity: 1;
        }

        100% {
            transform: translateY(-100px) translateX(100px) rotate(360deg);
            opacity: 0;
        }
    }

    @keyframes orbFloat {
        /* Speed adjusted */
        0%,
        100% {
            transform: translateY(0px) translateX(0px) scale(1);
            opacity: 0.8;
        }

        25% {
            transform: translateY(-20px) translateX(10px) scale(1.2);
            opacity: 1;
        }

        50% {
            transform: translateY(-10px) translateX(-10px) scale(0.8);
            opacity: 0.6;
        }

        75% {
            transform: translateY(-30px) translateX(5px) scale(1.1);
            opacity: 0.9;
        }
    }

    @keyframes taskadeBackgroundShift {
        /* Speed adjusted */
        0%,
        100% {
            background:
                linear-gradient(
                    180deg,
                    rgba(15, 15, 20, 0.4) 0%,
                    rgba(8, 8, 12, 0.98) 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.22) 0%,
                    rgba(99, 102, 241, 0.08) 50%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(168, 85, 247, 0.18) 0%,
                    rgba(168, 85, 247, 0.06) 70%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.16) 0%,
                    rgba(59, 130, 246, 0.05) 80%,
                    transparent 100%
                );
        }

        20% {
            background:
                linear-gradient(
                    180deg,
                    rgba(15, 15, 20, 0.4) 0%,
                    rgba(8, 8, 12, 0.98) 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(139, 92, 246, 0.25) 0%,
                    rgba(139, 92, 246, 0.09) 55%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.2) 0%,
                    rgba(99, 102, 241, 0.07) 65%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(147, 197, 253, 0.12) 0%,
                    rgba(147, 197, 253, 0.04) 85%,
                    transparent 100%
                );
        }

        40% {
            background:
                linear-gradient(
                    180deg,
                    rgba(15, 15, 20, 0.4) 0%,
                    rgba(8, 8, 12, 0.98) 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.24) 0%,
                    rgba(59, 130, 246, 0.08) 52%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(139, 92, 246, 0.19) 0%,
                    rgba(139, 92, 246, 0.06) 72%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(168, 85, 247, 0.16) 0%,
                    rgba(168, 85, 247, 0.05) 78%,
                    transparent 100%
                );
        }

        60% {
            background:
                linear-gradient(
                    180deg,
                    rgba(15, 15, 20, 0.4) 0%,
                    rgba(8, 8, 12, 0.98) 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(147, 197, 253, 0.22) 0%,
                    rgba(147, 197, 253, 0.08) 58%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(59, 130, 246, 0.18) 0%,
                    rgba(59, 130, 246, 0.06) 68%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(99, 102, 241, 0.15) 0%,
                    rgba(99, 102, 241, 0.05) 82%,
                    transparent 100%
                );
        }

        80% {
            background:
                linear-gradient(
                    180deg,
                    rgba(15, 15, 20, 0.4) 0%,
                    rgba(8, 8, 12, 0.98) 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(168, 85, 247, 0.24) 0%,
                    rgba(168, 85, 247, 0.09) 54%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(147, 197, 253, 0.19) 0%,
                    rgba(147, 197, 253, 0.06) 74%,
                    transparent 100%
                ),
                linear-gradient(
                    0deg,
                    rgba(139, 92, 246, 0.13) 0%,
                    rgba(139, 92, 246, 0.04) 84%,
                    transparent 100%
                );
        }
    }

    @keyframes pulse {
        0%,
        100% {
            transform: scale(1);
            opacity: 0.7;
        }

        50% {
            transform: scale(1.1);
            opacity: 1;
        }
    }

    .hero .subtitle {
        font-size: 22px;
        max-width: 700px;
        margin: 0 auto 48px auto;
        color: var(--text-secondary);
        line-height: 1.5;
    }

    .hero .cta-group {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 80px;
    }

    /* ------------------- */
    /* --- Problem Section --- */
    /* ------------------- */
    .problem-section {
        background-color: var(--bg-color);
        padding: 50px 0;
    }

    .problem-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 32px;
    }

    .problem-card {
        background: linear-gradient(
            135deg,
            var(--surface-color) 0%,
            rgba(26, 26, 34, 0.5) 100%
        );
        padding: 40px;
        border-radius: 16px;
        border: 1px solid var(--border-color);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .problem-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, #8b5cf6, #a78bfa);
        transform: scaleX(0);
        transition: transform 0.4s ease;
    }

    .problem-card:hover::before {
        transform: scaleX(1);
    }

    .problem-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 16px 40px rgba(139, 92, 246, 0.15);
        border-color: rgba(139, 92, 246, 0.4);
    }

    .problem-card .icon {
        margin-bottom: 24px;
        width: 96px;
        height: 96px;
        background: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .problem-card .icon svg {
        width: 80px;
        height: 80px;
        fill: #8b5cf6;
    }

    .problem-card h3 {
        font-size: 22px;
        margin-bottom: 16px;
        color: var(--text-primary);
    }

    .problem-card p {
        color: var(--text-secondary);
        line-height: 1.6;
    }

    /* ------------------- */
    /* --- Partners Section --- */
    /* ------------------- */
    .partners-section {
        padding: 40px 0;
        /* Adjusted padding */
        background-color: var(--surface-color);
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
    }

    .process-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 40px;
        margin-top: 64px;
    }

    .process-step {
        text-align: center;
        padding: 32px;
        border-radius: 12px;
        background: linear-gradient(
            135deg,
            var(--surface-color) 0%,
            rgba(26, 26, 34, 0.8) 100%
        );
        border: 1px solid var(--border-color);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .process-step::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(
            90deg,
            var(--accent-color),
            var(--accent-hover)
        );
        transform: scaleX(0);
        transition: transform 0.4s ease;
    }

    .process-step:hover::before {
        transform: scaleX(1);
    }

    .process-step:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(74, 85, 255, 0.15);
        border-color: rgba(74, 85, 255, 0.3);
    }

    .process-step .number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-family: "Plus Jakarta Sans", "Work Sans", sans-serif;
        font-size: 50px;
        font-weight: 700;
        color: transparent;
        margin-bottom: 20px;
        margin-top: -8px;
        position: relative;
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background-color: #000000;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .process-step .number::before {
        content: attr(data-number);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-family: "Plus Jakarta Sans", "Work Sans", sans-serif;
        font-size: 50px;
        font-weight: 700;
        background: linear-gradient(135deg, #b366cf 0%, #8b5cf6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        z-index: 2;
    }

    .process-step .number::after {
        content: "";
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: linear-gradient(135deg, #b366cf 0%, #8b5cf6 100%);
        z-index: -1;
        opacity: 0.1;
    }

    .process-step h3 {
        font-size: 22px;
        margin-bottom: 12px;
    }

    .process-step p {
        color: var(--text-secondary);
    }

    /* ------------------- */
    /* --- Our Solutions Section --- */
    /* ------------------- */
    .solutions-section {
        background-color: var(--bg-color);
    }

    .solutions-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 80px 40px;
        margin: 0;
    }

    .solution-card {
        background: transparent;
        border-radius: 0;
        overflow: visible;
        transition: all 0.3s ease;
        position: relative;
        border: none;
        box-shadow: none;
    }

    .solution-card:hover {
        transform: translateY(-5px);
    }

    .solution-image {
        width: 100%;
        height: 220px;
        object-fit: cover;
        border-radius: 16px;
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }

    .solution-card:hover .solution-image {
        transform: scale(1.02);
    }

    .solution-content {
        padding: 0;
    }

    .solution-card h3 {
        font-size: 25px;
        font-weight: 600;
        margin-bottom: 12px;
        line-height: 1.3;
        min-height: 52px;
        display: flex;
        align-items: center;
    }

    .solution-card .description {
        font-size: 16px;
        line-height: 1.6;
        color: var(--text-secondary);
        margin-bottom: 16px;
        min-height: 84px;
    }

    .solution-card .time-saved {
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        background: rgba(74, 85, 255, 0.1);
        padding: 6px 12px;
        border-radius: 10px;
        display: inline-block;
    }

    .solution-card .description {
        color: var(--text-secondary);
        margin-bottom: 16px;
        line-height: 1.6;
    }

    .solution-card .time-saved {
        background: linear-gradient(
            135deg,
            rgba(74, 85, 255, 0.1),
            rgba(74, 85, 255, 0.2)
        );
        border: 1px solid var(--accent-color);
        border-radius: 10px;
        padding: 5px 16px;
        font-size: 14px;
        font-weight: 600;
        color: #ffffff;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        backdrop-filter: blur(10px);
    }

    .solution-card .time-saved::before {
        content: "⚡";
        font-size: 22px;
    }

    /* Solution Tags */
    .solution-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 16px;
    }

    .solution-tags .tag {
        background: linear-gradient(
            135deg,
            rgba(74, 85, 255, 0.15),
            rgba(74, 85, 255, 0.25)
        );
        border: 1px solid rgba(74, 85, 255, 0.3);
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 12px;
        font-weight: 500;
        color: var(--accent-color);
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .solution-tags .tag:hover {
        background: linear-gradient(
            135deg,
            rgba(74, 85, 255, 0.25),
            rgba(74, 85, 255, 0.35)
        );
        transform: translateY(-1px);
    }

    /* ------------------- */
    /* --- Case Studies Section --- */
    /* ------------------- */
    #case-studies {
        position: relative;
        background:
            linear-gradient(
                135deg,
                rgba(11, 11, 15, 0.9) 0%,
                rgba(26, 26, 34, 0.95) 100%
            ),
            radial-gradient(
                ellipse at 20% 80%,
                rgba(80, 26, 200, 0.15) 0%,
                transparent 60%
            ),
            radial-gradient(
                ellipse at 80% 20%,
                rgba(179, 102, 207, 0.12) 0%,
                transparent 70%
            ),
            radial-gradient(
                ellipse at 50% 50%,
                rgba(67, 83, 255, 0.1) 0%,
                transparent 80%
            );
        border-top: 1px solid var(--border-color);
        overflow: hidden;
        animation: subtleBackgroundShift 30s ease-in-out infinite;
    }

    #case-studies::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(
                45deg,
                transparent 30%,
                rgba(80, 26, 200, 0.08) 50%,
                transparent 70%
            ),
            linear-gradient(
                -45deg,
                transparent 40%,
                rgba(179, 102, 207, 0.06) 60%,
                transparent 80%
            ),
            linear-gradient(
                90deg,
                transparent 35%,
                rgba(67, 83, 255, 0.06) 55%,
                transparent 75%
            );
        z-index: 1;
        animation: subtleColorShift 25s ease-in-out infinite;
    }

    #case-studies::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(
                circle at 30% 40%,
                rgba(80, 26, 200, 0.06) 0%,
                transparent 50%
            ),
            radial-gradient(
                circle at 70% 60%,
                rgba(179, 102, 207, 0.05) 0%,
                transparent 55%
            ),
            radial-gradient(
                circle at 50% 80%,
                rgba(67, 83, 255, 0.04) 0%,
                transparent 60%
            );
        z-index: 2;
        animation: subtleParticleFloat 20s ease-in-out infinite;
    }

    #case-studies .container {
        position: relative;
        z-index: 3;
    }

    /* Mobile optimization for video */
    @media (max-width: 768px) {
        .video-background video {
            opacity: 0.2;
        }
    }

    @keyframes subtleBackgroundShift {
        0%,
        100% {
            background:
                linear-gradient(
                    135deg,
                    rgba(11, 11, 15, 0.9) 0%,
                    rgba(26, 26, 34, 0.95) 100%
                ),
                radial-gradient(
                    ellipse at 20% 80%,
                    rgba(80, 26, 200, 0.15) 0%,
                    transparent 60%
                ),
                radial-gradient(
                    ellipse at 80% 20%,
                    rgba(179, 102, 207, 0.12) 0%,
                    transparent 70%
                ),
                radial-gradient(
                    ellipse at 50% 50%,
                    rgba(67, 83, 255, 0.1) 0%,
                    transparent 80%
                );
        }
        33% {
            background:
                linear-gradient(
                    135deg,
                    rgba(11, 11, 15, 0.9) 0%,
                    rgba(26, 26, 34, 0.95) 100%
                ),
                radial-gradient(
                    ellipse at 70% 30%,
                    rgba(179, 102, 207, 0.18) 0%,
                    transparent 65%
                ),
                radial-gradient(
                    ellipse at 30% 70%,
                    rgba(67, 83, 255, 0.14) 0%,
                    transparent 75%
                ),
                radial-gradient(
                    ellipse at 90% 90%,
                    rgba(80, 26, 200, 0.12) 0%,
                    transparent 70%
                );
        }
        66% {
            background:
                linear-gradient(
                    135deg,
                    rgba(11, 11, 15, 0.9) 0%,
                    rgba(26, 26, 34, 0.95) 100%
                ),
                radial-gradient(
                    ellipse at 60% 40%,
                    rgba(67, 83, 255, 0.16) 0%,
                    transparent 62%
                ),
                radial-gradient(
                    ellipse at 40% 60%,
                    rgba(80, 26, 200, 0.14) 0%,
                    transparent 68%
                ),
                radial-gradient(
                    ellipse at 10% 10%,
                    rgba(179, 102, 207, 0.1) 0%,
                    transparent 85%
                );
        }
    }

    @keyframes subtleColorShift {
        0%,
        100% {
            background:
                linear-gradient(
                    45deg,
                    transparent 30%,
                    rgba(80, 26, 200, 0.08) 50%,
                    transparent 70%
                ),
                linear-gradient(
                    -45deg,
                    transparent 40%,
                    rgba(179, 102, 207, 0.06) 60%,
                    transparent 80%
                ),
                linear-gradient(
                    90deg,
                    transparent 35%,
                    rgba(67, 83, 255, 0.06) 55%,
                    transparent 75%
                );
        }
        50% {
            background:
                linear-gradient(
                    45deg,
                    transparent 25%,
                    rgba(179, 102, 207, 0.1) 55%,
                    transparent 75%
                ),
                linear-gradient(
                    -45deg,
                    transparent 35%,
                    rgba(67, 83, 255, 0.08) 65%,
                    transparent 85%
                ),
                linear-gradient(
                    90deg,
                    transparent 30%,
                    rgba(80, 26, 200, 0.08) 60%,
                    transparent 80%
                );
        }
    }

    @keyframes subtleParticleFloat {
        0%,
        100% {
            background:
                radial-gradient(
                    circle at 30% 40%,
                    rgba(80, 26, 200, 0.06) 0%,
                    transparent 50%
                ),
                radial-gradient(
                    circle at 70% 60%,
                    rgba(179, 102, 207, 0.05) 0%,
                    transparent 55%
                ),
                radial-gradient(
                    circle at 50% 80%,
                    rgba(67, 83, 255, 0.04) 0%,
                    transparent 60%
                );
        }
        33% {
            background:
                radial-gradient(
                    circle at 60% 20%,
                    rgba(179, 102, 207, 0.07) 0%,
                    transparent 45%
                ),
                radial-gradient(
                    circle at 20% 70%,
                    rgba(67, 83, 255, 0.06) 0%,
                    transparent 50%
                ),
                radial-gradient(
                    circle at 80% 50%,
                    rgba(80, 26, 200, 0.05) 0%,
                    transparent 65%
                );
        }
        66% {
            background:
                radial-gradient(
                    circle at 40% 80%,
                    rgba(67, 83, 255, 0.06) 0%,
                    transparent 48%
                ),
                radial-gradient(
                    circle at 80% 30%,
                    rgba(80, 26, 200, 0.056) 0%,
                    transparent 52%
                ),
                radial-gradient(
                    circle at 10% 60%,
                    rgba(179, 102, 207, 0.044) 0%,
                    transparent 58%
                );
        }
    }

    #case-studies .container {
        position: relative;
        z-index: 3;
    }

    .case-study-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }

    .case-study-card {
        background-color: var(--bg-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .case-study-card .category {
        font-size: 14px;
        font-weight: 500;
        color: var(--accent-color);
        margin-bottom: 16px;
    }

    .case-study-card .metric {
        font-family: "Plus Jakarta Sans", "Work Sans", sans-serif;
        font-size: 48px;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 8px;
    }

    .case-study-card .metric-label {
        font-size: 16px;
        color: var(--text-secondary);
        margin-bottom: 32px;
    }

    .case-study-card .testimonial {
        font-style: italic;
        color: #c0c0c0;
        border-left: 3px solid var(--accent-color);
        padding-left: 20px;
        margin-top: auto;
    }

    .case-study-card .testimonial-author {
        font-style: normal;
        font-weight: 600;
        color: var(--text-primary);
        margin-top: 16px;
        display: block;
    }

    /* ------------------- */
    /* --- CTA Section --- */
    /* ------------------- */
    .cta-section {
        text-align: center;
    }

    .cta-section .section-title {
        font-size: 54px;
        max-width: 950px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-section .section-subtitle {
        margin-bottom: 40px;
    }

    /* ------------------- */
    /* --- Footer --- */
    /* ------------------- */
    .footer {
        border-top: 1px solid var(--border-color);
        padding: 40px 0;
        color: var(--text-secondary);
        font-size: 14px;
    }

    .footer .container {
        padding: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .footer-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .footer-text {
        flex: 1;
    }

    .footer-logo {
        flex-shrink: 0;       
    }

    .footer-logo a {
        display: inline-block;
        transition: transform 0.3s ease;
    }

    .footer-logo a:hover {
        transform: scale(1.05);
    }

    .footer-logo img {
        height: 40px;
        width: auto;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    .footer-logo a:hover img {
        opacity: 1;
    }

    /* ------------------- */
    /* --- Title Colors --- */
    /* ------------------- */
    .title-white {
        color: #ffffff;
    }

    .title-pink {
        color: #e879f9;
    }

    /* ------------------- */
    /* --- Language Selector --- */
    /* ------------------- */
    .language-select {
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 6px;
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        outline: none;
    }

    .language-select:hover {
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
    }

    .language-select:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 2px rgba(74, 85, 255, 0.2);
    }

    .language-select option {
        background-color: #1a1a1a;
        color: #ffffff;
        padding: 8px;
    }

    /* Mobile Language Selector */
    .mobile-language-selector {
        margin: 20px 0;
        padding: 20px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-language-title {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .mobile-language-select {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: #ffffff;
        font-size: 16px;
        font-weight: 500;
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        outline: none;
    }

    .mobile-language-select:focus {
        border-color: var(--accent-color);
        background: rgba(255, 255, 255, 0.1);
    }

    .mobile-language-select option {
        background-color: #1a1a1a;
        color: #ffffff;
        padding: 12px;
    }

    /* ------------------- */
    /* --- Contact Form Styles --- */
    /* ------------------- */
    .contact-form-section {
        padding: 120px 0;
        min-height: calc(100vh - 160px);
        display: flex;
        align-items: center;
        position: relative;
        background:
            linear-gradient(
                135deg,
                rgba(11, 11, 15, 0.9) 0%,
                rgba(26, 26, 34, 0.95) 100%
            ),
            radial-gradient(
                ellipse at 20% 80%,
                rgba(80, 26, 200, 0.15) 0%,
                transparent 60%
            ),
            radial-gradient(
                ellipse at 80% 20%,
                rgba(179, 102, 207, 0.12) 0%,
                transparent 70%
            ),
            radial-gradient(
                ellipse at 50% 50%,
                rgba(67, 83, 255, 0.1) 0%,
                transparent 80%
            );
        border-top: 1px solid var(--border-color);
        overflow: hidden;
        animation: subtleBackgroundShift 45s ease-in-out infinite;
    }

    .contact-form-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(
                45deg,
                transparent 30%,
                rgba(80, 26, 200, 0.08) 50%,
                transparent 70%
            ),
            linear-gradient(
                -45deg,
                transparent 40%,
                rgba(179, 102, 207, 0.06) 60%,
                transparent 80%
            ),
            linear-gradient(
                90deg,
                transparent 35%,
                rgba(67, 83, 255, 0.06) 55%,
                transparent 75%
            );
        z-index: 1;
        animation: subtleColorShift 35s ease-in-out infinite;
    }

    .contact-form-section::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(
                circle at 30% 40%,
                rgba(80, 26, 200, 0.06) 0%,
                transparent 50%
            ),
            radial-gradient(
                circle at 70% 60%,
                rgba(179, 102, 207, 0.05) 0%,
                transparent 55%
            ),
            radial-gradient(
                circle at 50% 80%,
                rgba(67, 83, 255, 0.04) 0%,
                transparent 60%
            );
        z-index: 2;
        animation: subtleParticleFloat 40s ease-in-out infinite;
    }

    .contact-form-container {
        max-width: 800px;
        margin: 0 auto;
        background: linear-gradient(
            135deg,
            var(--surface-color) 0%,
            rgba(26, 26, 34, 0.8) 100%
        );
        padding: 60px;
        border-radius: 20px;
        border: 1px solid var(--border-color);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 3;
        backdrop-filter: blur(10px);
    }

    .contact-form-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(
            90deg,
            var(--accent-color),
            var(--accent-hover)
        );
        border-radius: 20px 20px 0 0;
    }

    .contact-form-container .section-title {
        font-size: 42px;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .contact-form-container .section-subtitle {
        font-size: 18px;
        margin-bottom: 40px;
        color: var(--text-secondary);
    }

    .form-group {
        margin-bottom: 24px;
    }

    .form-group label {
        display: block;
        font-size: 14px;
        color: var(--text-primary);
        margin-bottom: 10px;
        font-weight: 600;
        font-family: "Plus Jakarta Sans", "Work Sans", sans-serif;
        letter-spacing: 0.02em;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        background: linear-gradient(
            135deg,
            var(--bg-color) 0%,
            rgba(11, 11, 15, 0.8) 100%
        );
        color: var(--text-primary);
        font-size: 16px;
        font-family: '"Work Sans"', sans-serif;
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
    }

    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--accent-color);
        box-shadow:
            0 0 0 3px rgba(74, 85, 255, 0.2),
            0 4px 20px rgba(74, 85, 255, 0.1);
        transform: translateY(-2px);
    }

    .form-group input:hover,
    .form-group textarea:hover {
        border-color: rgba(74, 85, 255, 0.5);
        transform: translateY(-1px);
    }

    .form-group textarea {
        min-height: 120px;
        resize: vertical;
    }

    .form-row {
        display: flex;
        gap: 24px;
    }

    .form-row .form-group {
        flex: 1;
    }

    .form-group select {
        width: 100%;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        background: linear-gradient(
            135deg,
            var(--bg-color) 0%,
            rgba(11, 11, 15, 0.8) 100%
        );
        color: var(--text-primary);
        font-size: 16px;
        font-family: '"Work Sans"', sans-serif;
        transition: all 0.3s ease;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23b3b5bd'%3E%3Cpath d='M7 10l5 5 5-5H7z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 20px center;
        background-size: 1em;
        backdrop-filter: blur(5px);
    }

    .form-group select:focus {
        outline: none;
        border-color: var(--accent-color);
        box-shadow:
            0 0 0 3px rgba(74, 85, 255, 0.2),
            0 4px 20px rgba(74, 85, 255, 0.1);
        transform: translateY(-2px);
    }

    .form-group select:hover {
        border-color: rgba(74, 85, 255, 0.5);
        transform: translateY(-1px);
    }

    .form-group select option {
        background-color: #1a1a1a;
        color: #ffffff;
        padding: 12px;
        border: none;
    }

    .form-group select option:hover {
        background-color: var(--accent-color);
        color: #ffffff;
    }

    .form-group select option:checked {
        background-color: var(--accent-color);
        color: #ffffff;
    }

    .form-group.required label::after {
        content: " *";
        color: var(--accent-color);
    }

    .form-intro-text {
        font-size: 16px;
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 32px;
        text-align: left;
        padding: 24px;
        background: rgba(74, 85, 255, 0.05);
        border-radius: 12px;
        border-left: 3px solid var(--accent-color);
    }

    /* Floating Particles */
    .contact-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    /* Data Stream Effect */
    .contact-datastream {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    /* ------------------- */
    /* --- Dashboard Styles --- */
    /* ------------------- */
    .dashboard-section {
        padding: 120px 0;
        background: var(--bg-color);
        min-height: 100vh;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 24px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(
            135deg,
            var(--surface-color) 0%,
            rgba(26, 26, 34, 0.8) 100%
        );
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 24px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(74, 85, 255, 0.15);
        border-color: rgba(74, 85, 255, 0.3);
    }

    .stat-icon {
        font-size: 32px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(74, 85, 255, 0.1);
        border-radius: 12px;
    }

    .stat-content h3 {
        font-size: 28px;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .stat-content p {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 4px 0 0 0;
    }

    .charts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 24px;
        margin-bottom: 40px;
    }

    .chart-card {
        background: linear-gradient(
            135deg,
            var(--surface-color) 0%,
            rgba(26, 26, 34, 0.8) 100%
        );
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 24px;
    }

    .chart-card h3 {
        font-size: 18px;
        color: var(--text-primary);
        margin-bottom: 20px;
    }

    .chart-content {
        min-height: 200px;
    }

    .chart-bar {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
    }

    .bar-label {
        min-width: 120px;
        font-size: 14px;
        color: var(--text-secondary);
    }

    .bar-container {
        flex: 1;
        position: relative;
        height: 24px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        overflow: hidden;
    }

    .bar-fill {
        height: 100%;
        background: linear-gradient(
            90deg,
            var(--accent-color),
            var(--accent-hover)
        );
        border-radius: 12px;
        transition: width 0.5s ease;
    }

    .bar-value {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        color: var(--text-primary);
        font-weight: 600;
    }

    .realtime-section {
        background: linear-gradient(
            135deg,
            var(--surface-color) 0%,
            rgba(26, 26, 34, 0.8) 100%
        );
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 24px;
    }

    .realtime-section h3 {
        font-size: 18px;
        color: var(--text-primary);
        margin-bottom: 20px;
    }

    .realtime-feed {
        max-height: 300px;
        overflow-y: auto;
    }

    .feed-item {
        padding: 12px;
        background: rgba(74, 85, 255, 0.05);
        border-left: 3px solid var(--accent-color);
        border-radius: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        color: var(--text-secondary);
        animation: fadeInUp 0.3s ease;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        color: var(--text-secondary);
        font-style: italic;
    }

    .no-data {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        color: var(--text-secondary);
        font-style: italic;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 8px;
        border: 1px dashed var(--border-color);
    }

    .data-table {
        max-height: 400px;
        overflow-y: auto;
    }

    .data-table-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        gap: 12px;
        padding: 12px;
        background: rgba(74, 85, 255, 0.1);
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        font-size: 14px;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
    }

    .data-table-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        gap: 12px;
        padding: 12px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        font-size: 13px;
        color: var(--text-secondary);
        transition: background 0.2s ease;
    }

    .data-table-row:hover {
        background: rgba(74, 85, 255, 0.05);
    }

    .data-table-row:last-child {
        border-bottom: none;
        border-radius: 0 0 8px 8px;
    }

    .contact-name {
        font-weight: 600;
        color: var(--text-primary);
    }

    .contact-email {
        color: var(--accent-color);
    }

    .visit-page {
        font-family: monospace;
        background: rgba(255, 255, 255, 0.05);
        padding: 2px 6px;
        border-radius: 4px;
    }

    .visit-country {
        font-weight: 600;
    }

    .visit-ip {
        font-family: monospace;
        color: var(--text-secondary);
    }

    /* ------------------- */
    /* --- Responsive Design --- */
    /* ------------------- */
    @media (max-width: 1024px) {
        .process-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 32px;
        }

        .solutions-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }
    }

    @media (max-width: 900px) {
        .hero h1 {
            font-size: 42px;
        }

        .hero .subtitle {
            font-size: 17px;
        }

        .section-title {
            font-size: 42px;
        }

        .process-grid {
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .process-step {
            padding: 24px;
        }

        .process-step .number {
            font-size: 44px;
            margin-bottom: 16px;
            margin-top: -6px;
            width: 100px;
            height: 100px;
        }

        .case-study-grid {
            grid-template-columns: 1fr;
        }

        .solutions-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .partners-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 24px;
        }

        /* Show hamburger menu on tablet and mobile */
        .nav-desktop {
            display: none;
        }

        .hamburger {
            display: flex;
        }

        .nav-mobile {
            display: block;
        }
    }

    @media (max-width: 600px) {
        body {
            padding-top: 70px;
            /* Reduced header height on mobile */
        }

        .header {
            padding: 12px 5%;
        }

        .logo {
            font-size: 20px;
        }

        .hamburger span {
            width: 22px;
        }

        .nav-mobile {
            width: 100%;
            padding: 90px 24px 24px 24px;
        }

        .container {
            padding: 80px 0;
        }

        .hero {
            padding-top: 40px;
            padding-bottom: 80px;
            background-attachment: scroll;
            /* Better performance on mobile */
            background-size: cover, cover, 120%;
            /* Slightly larger for better mobile view */
            margin-left: calc(-50vw + 50%);
            width: 100vw;
        }

        .hero::before {
            opacity: 0.8;
            /* Stronger overlay on mobile for better text readability */
        }

        .hero-content {
            padding: 0 10%;
            /* More padding on mobile for better readability */
        }

        /* Reduce animations on mobile for performance */
        .particle {
            display: none;
        }

        .glowing-orb {
            animation-duration: 20s;
        }

        .hero::before {
            animation-duration: 12s;
        }

        .hero::after {
            animation-duration: 16s;
        }

        .hero h1 {
            font-size: 36px;
        }

        .hero .subtitle {
            font-size: 16px;
        }

        .hero .cta-group {
            flex-direction: column;
        }

        .section-title {
            font-size: 36px;
        }

        .section-subtitle {
            font-size: 16px;
            margin-bottom: 48px;
        }

        .problem-grid {
            grid-template-columns: 1fr;
        }

        .process-grid {
            grid-template-columns: 1fr;
            gap: 32px;
        }

        .process-step {
            padding: 32px 24px;
        }

        .process-step .number {
            font-size: 42px;
            margin-bottom: 14px;
            margin-top: -4px;
            width: 80px;
            height: 80px;
        }

        /* Problem cards icons responsive */
        .problem-card .icon {
            width: 80px;
            height: 80px;
        }

        .problem-card .icon svg {
            width: 60px;
            height: 60px;
        }

        /* Title colors responsive */
        .title-white,
        .title-pink {
            display: inline;
        }

        .cta-section .section-title {
            font-size: 42px;
        }

        .solutions-grid {
            grid-template-columns: 1fr;
            gap: 60px;
        }

        .solution-image {
            height: 180px;
            border-radius: 12px;
        }

        .solution-card h3 {
            font-size: 22px;
            min-height: auto;
        }

        .solution-card .description {
            min-height: auto;
        }

        /* Solution tags responsive */
        .solution-tags {
            gap: 6px;
            margin-top: 12px;
        }

        .solution-tags .tag {
            font-size: 11px;
            padding: 3px 10px;
        }

        .partners-grid {
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
        }

        .partner-logo {
            padding: 10px;
            min-height: 60px;
        }

        .partner-logo img {
            max-width: 100px;
            max-height: 30px;
        }

        .partner-placeholder {
            width: 100px;
            height: 30px;
            font-size: 10px;
        }

        .case-study-card {
            padding: 24px;
        }

        .case-study-card .metric {
            font-size: 36px;
        }

        /* Dashboard responsive */
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .charts-grid {
            grid-template-columns: 1fr;
            gap: 24px;
        }

        /* Contact form responsive */
        .form-row {
            flex-direction: column;
        }

        .contact-form-container {
            padding: 24px;
        }

        /* Login form responsive */
        .login-container {
            margin: 20px;
            padding: 24px;
        }

        .footer .container {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }

        .footer-content {
            flex-direction: column;
            gap: 20px;
        }

        .footer-logo {
            margin-left: 0;
        }
    }

    /* Extra small devices (320px and down) */
    @media (max-width: 320px) {
        .hero h1 {
            font-size: 28px;
        }

        .section-title {
            font-size: 32px;
        }

        .hero-content {
            padding: 0 15px;
        }

        .container {
            padding: 50px 0;
        }

        .process-step {
            padding: 20px 16px;
        }

        .solution-card {
            padding: 16px;
        }

        .case-study-card {
            padding: 20px;
        }
    }
</style>
