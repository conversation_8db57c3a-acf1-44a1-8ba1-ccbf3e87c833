#!/usr/bin/env node

/**
 * Quality Assurance Script for Blog Generation
 *
 * This script ensures that blog generation produces high-quality,
 * relevant content without drift into unrelated topics.
 */

import * as dotenv from "dotenv";
import { dataForSEO } from "../src/utils/dataforseo.js";
import chalk from "chalk";
import ora from "ora";
import * as fs from "fs/promises";
import * as path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// QA Test Cases
const QA_TEST_CASES = [
  {
    topic: "AI Customer Service Automation",
    expectedKeywords: ["ai customer service", "automated support", "chatbot"],
    bannedKeywords: ["doctor", "fairfield", "eye", "medical", "dre"],
    minSearchVolume: 100,
    category: "ai-agents"
  },
  {
    topic: "Multi-Agent AI Systems",
    expectedKeywords: ["multi-agent", "ai orchestration", "agent coordination"],
    bannedKeywords: ["doctor", "medical", "celebrity", "location"],
    minSearchVolume: 50,
    category: "ai-agents"
  },
  {
    topic: "Workflow Automation with AI",
    expectedKeywords: ["workflow automation", "process automation", "ai automation"],
    bannedKeywords: ["doctor", "medical", "personal", "unrelated brands"],
    minSearchVolume: 100,
    category: "automation"
  }
];

// Quality checks
interface QualityCheckResult {
  passed: boolean;
  issues: string[];
  warnings: string[];
  score: number;
}

class BlogQualityAssurance {
  /**
   * Run comprehensive QA tests
   */
  async runQATests(): Promise<void> {
    console.log(chalk.bold.cyan("\n🔍 Blog Generation Quality Assurance\n"));

    const results = [];

    for (const testCase of QA_TEST_CASES) {
      console.log(chalk.yellow(`\nTesting: ${testCase.topic}`));
      console.log(chalk.dim("─".repeat(50)));

      try {
        const result = await this.testBlogGeneration(testCase);
        results.push(result);

        if (result.passed) {
          console.log(chalk.green("✅ PASSED"));
        } else {
          console.log(chalk.red("❌ FAILED"));
          result.issues.forEach(issue => {
            console.log(chalk.red(`   - ${issue}`));
          });
        }

        if (result.warnings.length > 0) {
          console.log(chalk.yellow("⚠️  Warnings:"));
          result.warnings.forEach(warning => {
            console.log(chalk.yellow(`   - ${warning}`));
          });
        }

        console.log(chalk.dim(`   Quality Score: ${result.score}/100`));

      } catch (error: any) {
        console.log(chalk.red(`❌ ERROR: ${error.message}`));
        results.push({
          passed: false,
          issues: [`Test failed with error: ${error.message}`],
          warnings: [],
          score: 0
        });
      }
    }

    // Summary
    console.log(chalk.cyan("\n📊 QA Summary"));
    console.log(chalk.dim("─".repeat(50)));

    const passed = results.filter(r => r.passed).length;
    const failed = results.filter(r => !r.passed).length;
    const avgScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;

    console.log(`Total Tests: ${results.length}`);
    console.log(chalk.green(`Passed: ${passed}`));
    console.log(chalk.red(`Failed: ${failed}`));
    console.log(`Average Score: ${avgScore.toFixed(1)}/100`);

    if (failed > 0) {
      console.log(chalk.red("\n❌ QA Failed - Fix issues before deploying"));
      process.exit(1);
    } else {
      console.log(chalk.green("\n✅ All QA tests passed!"));
    }
  }

  /**
   * Test individual blog generation
   */
  private async testBlogGeneration(testCase: any): Promise<QualityCheckResult> {
    const issues: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    // Test 1: Generate blog with dry run to check keywords
    const spinner = ora("Analyzing keywords...").start();

    try {
      // Create a temporary test that captures keyword selection
      const keywordAnalysis = await this.captureKeywordAnalysis(
        testCase.topic,
        testCase.category
      );

      spinner.succeed("Keyword analysis complete");

      // Check 1: Banned keywords
      const foundBannedKeywords = keywordAnalysis.keywords.filter((kw: any) => {
        return testCase.bannedKeywords.some((banned: string) =>
          kw.keyword.toLowerCase().includes(banned.toLowerCase())
        );
      });

      if (foundBannedKeywords.length > 0) {
        issues.push(`Found banned keywords: ${foundBannedKeywords.map((k: any) => k.keyword).join(", ")}`);
        score -= 30;
      }

      // Check 2: Expected keywords presence
      const hasExpectedKeywords = testCase.expectedKeywords.some((expected: string) =>
        keywordAnalysis.keywords.some((kw: any) =>
          kw.keyword.toLowerCase().includes(expected.toLowerCase())
        )
      );

      if (!hasExpectedKeywords) {
        issues.push("Missing expected keywords for topic");
        score -= 20;
      }

      // Check 3: Minimum search volume
      const lowVolumeKeywords = keywordAnalysis.keywords.filter(
        (kw: any) => kw.search_volume < testCase.minSearchVolume
      );

      if (lowVolumeKeywords.length > keywordAnalysis.keywords.length * 0.5) {
        warnings.push("More than 50% of keywords have low search volume");
        score -= 10;
      }

      // Check 4: Topic relevance
      const irrelevantKeywords = keywordAnalysis.keywords.filter((kw: any) => {
        const relevanceScore = this.calculateRelevance(kw.keyword, testCase.topic);
        return relevanceScore < 0.3;
      });

      if (irrelevantKeywords.length > 0) {
        issues.push(`Found ${irrelevantKeywords.length} irrelevant keywords`);
        score -= 15;
      }

      // Check 5: Language consistency
      if (keywordAnalysis.language !== "en") {
        const englishKeywords = keywordAnalysis.keywords.filter((kw: any) =>
          this.isEnglish(kw.keyword)
        );

        if (englishKeywords.length > 0) {
          warnings.push(`Found English keywords in ${keywordAnalysis.language} content`);
          score -= 5;
        }
      }

    } catch (error: any) {
      spinner.fail("Analysis failed");
      throw error;
    }

    return {
      passed: issues.length === 0,
      issues,
      warnings,
      score: Math.max(0, score)
    };
  }

  /**
   * Capture keyword analysis without generating content
   */
  private async captureKeywordAnalysis(topic: string, category: string): Promise<any> {
    // This would integrate with the DataForSEO system to get keyword analysis
    // For now, we'll simulate the analysis

    // In real implementation, this would call the DataForSEO API
    // and return actual keyword data for analysis

    return {
      topic,
      category,
      language: "en",
      keywords: [
        { keyword: "ai customer service", search_volume: 2400, relevance: 0.9 },
        { keyword: "automated support", search_volume: 1900, relevance: 0.85 },
        { keyword: "chatbot customer service", search_volume: 1100, relevance: 0.8 }
      ]
    };
  }

  /**
   * Calculate keyword relevance to topic
   */
  private calculateRelevance(keyword: string, topic: string): number {
    const keywordWords = keyword.toLowerCase().split(" ");
    const topicWords = topic.toLowerCase().split(" ");

    let matches = 0;
    keywordWords.forEach(kWord => {
      topicWords.forEach(tWord => {
        if (kWord.includes(tWord) || tWord.includes(kWord)) {
          matches++;
        }
      });
    });

    return Math.min(1, matches / Math.max(keywordWords.length, topicWords.length));
  }

  /**
   * Check if text is in English
   */
  private isEnglish(text: string): boolean {
    // Simple check for common English patterns
    const englishPatterns = /\b(the|is|are|was|were|been|have|has|had|do|does|did)\b/i;
    return englishPatterns.test(text);
  }
}

// Additional QA utilities
class ContentQualityChecker {
  /**
   * Check generated content quality
   */
  static async checkContent(filepath: string): Promise<QualityCheckResult> {
    const issues: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    try {
      const content = await fs.readFile(filepath, "utf-8");

      // Extract frontmatter and content
      const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
      if (!frontmatterMatch) {
        issues.push("Missing frontmatter");
        score -= 20;
        return { passed: false, issues, warnings, score };
      }

      const frontmatter = frontmatterMatch[1];
      const bodyContent = content.substring(frontmatterMatch[0].length);

      // Check frontmatter quality
      const frontmatterChecks = this.checkFrontmatter(frontmatter);
      issues.push(...frontmatterChecks.issues);
      warnings.push(...frontmatterChecks.warnings);
      score -= frontmatterChecks.penalty;

      // Check content quality
      const contentChecks = this.checkBodyContent(bodyContent);
      issues.push(...contentChecks.issues);
      warnings.push(...contentChecks.warnings);
      score -= contentChecks.penalty;

    } catch (error: any) {
      issues.push(`Failed to read content: ${error.message}`);
      score = 0;
    }

    return {
      passed: issues.length === 0,
      issues,
      warnings,
      score: Math.max(0, score)
    };
  }

  private static checkFrontmatter(frontmatter: string): {
    issues: string[];
    warnings: string[];
    penalty: number;
  } {
    const issues: string[] = [];
    const warnings: string[] = [];
    let penalty = 0;

    // Check for banned keywords in tags
    const tagsMatch = frontmatter.match(/tags:\s*\[(.*?)\]/);
    if (tagsMatch) {
      const tags = tagsMatch[1];
      if (tags.includes("doctor") || tags.includes("fairfield") || tags.includes("eye")) {
        issues.push("Found irrelevant tags in frontmatter");
        penalty += 15;
      }
    }

    // Check keywords quality
    const keywordsMatch = frontmatter.match(/keywords:\s*\[(.*?)\]/);
    if (keywordsMatch) {
      const keywords = keywordsMatch[1];
      if (keywords.includes("doctor dre") || keywords.includes("near me")) {
        issues.push("Found irrelevant keywords in frontmatter");
        penalty += 20;
      }
    }

    return { issues, warnings, penalty };
  }

  private static checkBodyContent(content: string): {
    issues: string[];
    warnings: string[];
    penalty: number;
  } {
    const issues: string[] = [];
    const warnings: string[] = [];
    let penalty = 0;

    // Check for location-specific content
    if (content.includes("Fairfield") || content.includes("Connecticut")) {
      issues.push("Found location-specific content that wasn't requested");
      penalty += 15;
    }

    // Check for irrelevant topics
    if (content.includes("eye doctor") || content.includes("optometrist")) {
      issues.push("Content drifted to unrelated medical topics");
      penalty += 20;
    }

    // Check content length
    const wordCount = content.split(/\s+/).length;
    if (wordCount < 1000) {
      warnings.push("Content is shorter than recommended (< 1000 words)");
      penalty += 5;
    }

    return { issues, warnings, penalty };
  }
}

// Main execution
async function main() {
  const qa = new BlogQualityAssurance();

  try {
    await qa.runQATests();

    // Additional content checks if files exist
    console.log(chalk.cyan("\n📄 Checking existing content quality..."));

    const contentDir = path.join(__dirname, "../src/content/blog");
    try {
      const languages = await fs.readdir(contentDir);

      for (const lang of languages) {
        const langDir = path.join(contentDir, lang);
        const files = await fs.readdir(langDir);

        for (const file of files.slice(0, 3)) { // Check first 3 files
          if (file.endsWith(".md")) {
            console.log(chalk.dim(`\nChecking: ${lang}/${file}`));
            const result = await ContentQualityChecker.checkContent(
              path.join(langDir, file)
            );

            if (!result.passed) {
              console.log(chalk.red("  Issues found:"));
              result.issues.forEach(issue => {
                console.log(chalk.red(`    - ${issue}`));
              });
            } else {
              console.log(chalk.green("  ✓ Content quality OK"));
            }
          }
        }
      }
    } catch (error) {
      console.log(chalk.yellow("No existing content to check"));
    }

  } catch (error: any) {
    console.error(chalk.red("\n❌ QA process failed:"), error.message);
    process.exit(1);
  }
}

// Run QA tests
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error(chalk.red("Unexpected error:"), error);
    process.exit(1);
  });
}

export { BlogQualityAssurance, ContentQualityChecker };
