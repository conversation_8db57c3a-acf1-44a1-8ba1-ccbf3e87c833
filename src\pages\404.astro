---
import Layout from '../layouts/Layout.astro';
import { getLangFromUrl, useTranslations, localizePath } from '../i18n/utils';

// Get language from URL
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Define translations for 404 page
const translations = {
  en: {
    title: "Page Not Found",
    heading: "404",
    subheading: "Oops! Page not found",
    description: "The page you're looking for doesn't exist or has been moved. Let's get you back on track.",
    homeButton: "Back to Home",
    contactButton: "Contact Us",
    suggestionsTitle: "Here are some helpful links:",
    blog: "Visit our Blog",
    solutions: "Explore Solutions",
    about: "Learn About Us"
  },
  it: {
    title: "Pagina Non Trovata",
    heading: "404",
    subheading: "Ops! Pagina non trovata",
    description: "La pagina che stai cercando non esiste o è stata spostata. Ti aiutiamo a tornare sulla strada giusta.",
    homeButton: "Torna alla Home",
    contactButton: "<PERSON><PERSON><PERSON>ci",
    suggestionsTitle: "Ecco alcuni link utili:",
    blog: "Visita il nostro Blog",
    solutions: "Esplora le Soluzioni",
    about: "Chi Siamo"
  },
  es: {
    title: "Página No Encontrada",
    heading: "404",
    subheading: "¡Ups! Página no encontrada",
    description: "La página que buscas no existe o ha sido movida. Te ayudamos a volver al camino correcto.",
    homeButton: "Volver al Inicio",
    contactButton: "Contáctanos",
    suggestionsTitle: "Aquí hay algunos enlaces útiles:",
    blog: "Visita nuestro Blog",
    solutions: "Explora Soluciones",
    about: "Conócenos"
  }
};

const content = translations[lang] || translations.en;
---

<Layout title={content.title}>
  <main class="error-page">
    <div class="error-container">
      <div class="error-content">
        <h1 class="error-code">{content.heading}</h1>
        <h2 class="error-title">{content.subheading}</h2>
        <p class="error-description">{content.description}</p>

        <div class="error-actions">
          <a href={localizePath("/", lang)} class="btn-primary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            {content.homeButton}
          </a>
          <a href={localizePath("/contact", lang)} class="btn-secondary">
            {content.contactButton}
          </a>
        </div>

        <div class="suggestions">
          <p class="suggestions-title">{content.suggestionsTitle}</p>
          <div class="suggestions-links">
            <a href={localizePath("/blog", lang)} class="suggestion-link">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
                <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
              </svg>
              {content.blog}
            </a>
            <a href={localizePath("/", lang)}#solutions class="suggestion-link">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                <polyline points="2 17 12 22 22 17"></polyline>
                <polyline points="2 12 12 17 22 12"></polyline>
              </svg>
              {content.solutions}
            </a>
            <a href={localizePath("/", lang)}#about class="suggestion-link">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
              </svg>
              {content.about}
            </a>
          </div>
        </div>
      </div>

      <div class="error-graphic">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<style>
  .error-page {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    position: relative;
  }

  .error-container {
    max-width: 1200px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
  }

  .error-content {
    z-index: 2;
  }

  .error-code {
    font-size: 120px;
    font-weight: 900;
    margin: 0;
    background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 20px;
  }

  .error-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 16px 0;
    color: #ffffff;
  }

  .error-description {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 40px;
  }

  .error-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 60px;
  }

  .btn-primary,
  .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 28px;
    border-radius: 100px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
  }

  .btn-primary {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .suggestions {
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .suggestions-title {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .suggestions-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .suggestion-link {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s ease;
    padding: 8px 0;
  }

  .suggestion-link:hover {
    color: #8b5cf6;
    transform: translateX(5px);
  }

  .suggestion-link svg {
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  .suggestion-link:hover svg {
    opacity: 1;
  }

  .error-graphic {
    position: relative;
    height: 500px;
  }

  .floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(100px);
    opacity: 0.6;
  }

  .shape-1 {
    width: 300px;
    height: 300px;
    background: #8b5cf6;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation: float-1 20s infinite ease-in-out;
  }

  .shape-2 {
    width: 200px;
    height: 200px;
    background: #ec4899;
    bottom: 100px;
    right: 0;
    animation: float-2 15s infinite ease-in-out;
  }

  .shape-3 {
    width: 150px;
    height: 150px;
    background: #3b82f6;
    bottom: 50px;
    left: 50px;
    animation: float-3 25s infinite ease-in-out;
  }

  @keyframes float-1 {
    0%, 100% { transform: translate(-50%, 0) scale(1); }
    50% { transform: translate(-50%, -30px) scale(1.1); }
  }

  @keyframes float-2 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(-20px, -40px) scale(0.9); }
  }

  @keyframes float-3 {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(30px, -20px) scale(1.2); }
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .error-container {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }

    .error-graphic {
      height: 300px;
      order: -1;
    }

    .error-actions {
      justify-content: center;
    }

    .suggestions-links {
      align-items: center;
    }
  }

  @media (max-width: 768px) {
    .error-code {
      font-size: 80px;
    }

    .error-title {
      font-size: 24px;
    }

    .error-description {
      font-size: 16px;
    }

    .error-actions {
      flex-direction: column;
      width: 100%;
    }

    .btn-primary,
    .btn-secondary {
      width: 100%;
      justify-content: center;
    }

    .shape-1 { width: 200px; height: 200px; }
    .shape-2 { width: 150px; height: 150px; }
    .shape-3 { width: 100px; height: 100px; }
  }

  @media (max-width: 480px) {
    .error-page {
      padding: 20px;
    }

    .error-code {
      font-size: 60px;
    }

    .error-title {
      font-size: 20px;
    }

    .suggestions-links {
      gap: 8px;
    }

    .suggestion-link {
      font-size: 14px;
    }
  }
</style>
