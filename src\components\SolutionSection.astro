---
import { getLangFromAstro, useTranslations } from '../i18n/utils';

const lang = getLangFromAstro(Astro);
const t = useTranslations(lang);
---

<section id="soluzioni" class="solutions-section">
    <div class="container">
        <h2 class="section-title">{t("solutions.title")}</h2>
        <p class="section-subtitle">{t("solutions.subtitle")}</p>

        <div class="solutions-grid">
            <div class="solution-card">
                <img src="/images/automatizzazioni.png" alt="Gestione Appuntamenti" class="solution-image">
                <div class="solution-content">
                    <h3>{t("solutions.card1_title")}</h3>
                    <p class="description">{t("solutions.card1_desc")}</p>
                    <div class="solution-tags">
                        <span class="tag">{t("solutions.card1_tag1")}</span>
                        <span class="tag">{t("solutions.card1_tag2")}</span>
                        <span class="tag">{t("solutions.card1_tag3")}</span>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <img src="/images/notizie.png" alt="News Sportive" class="solution-image">
                <div class="solution-content">
                    <h3>{t("solutions.card2_title")}</h3>
                    <p class="description">{t("solutions.card2_desc")}</p>
                    <div class="solution-tags">
                        <span class="tag">{t("solutions.card2_tag1")}</span>
                        <span class="tag">{t("solutions.card2_tag2")}</span>
                        <span class="tag">{t("solutions.card2_tag3")}</span>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <img src="/images/avatar.png" alt="Avatar AR" class="solution-image">
                <div class="solution-content">
                    <h3>{t("solutions.card3_title")}</h3>
                    <p class="description">{t("solutions.card3_desc")}</p>
                    <div class="solution-tags">
                        <span class="tag">{t("solutions.card3_tag1")}</span>
                        <span class="tag">{t("solutions.card3_tag2")}</span>
                        <span class="tag">{t("solutions.card3_tag3")}</span>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <img src="/images/kdp.png" alt="Libri Kindle" class="solution-image">
                <div class="solution-content">
                    <h3>{t("solutions.card4_title")}</h3>
                    <p class="description">{t("solutions.card4_desc")}</p>
                    <div class="solution-tags">
                        <span class="tag">{t("solutions.card4_tag1")}</span>
                        <span class="tag">{t("solutions.card4_tag2")}</span>
                        <span class="tag">{t("solutions.card4_tag3")}</span>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <img src="/images/kate.png" alt="Analisi Dati" class="solution-image">
                <div class="solution-content">
                    <h3>{t("solutions.card5_title")}</h3>
                    <p class="description">{t("solutions.card5_desc")}</p>
                    <div class="solution-tags">
                        <span class="tag">{t("solutions.card5_tag1")}</span>
                        <span class="tag">{t("solutions.card5_tag2")}</span>
                        <span class="tag">{t("solutions.card5_tag3")}</span>
                    </div>
                </div>
            </div>
            <div class="solution-card">
                <img src="/images/img02.png" alt="Assistente Virtuale" class="solution-image">
                <div class="solution-content">
                    <h3>{t("solutions.card6_title")}</h3>
                    <p class="description">{t("solutions.card6_desc")}</p>
                    <div class="solution-tags">
                        <span class="tag">{t("solutions.card6_tag1")}</span>
                        <span class="tag">{t("solutions.card6_tag2")}</span>
                        <span class="tag">{t("solutions.card6_tag3")}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
