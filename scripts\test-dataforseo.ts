#!/usr/bin/env node

/**
 * DataForSEO Integration Test Script
 *
 * This script tests the real DataForSEO API integration to ensure
 * all endpoints are working correctly with actual data.
 *
 * Usage: npm run test:dataforseo
 */

import * as dotenv from "dotenv";
import { dataForSEO } from "../src/utils/dataforseo.js";
import chalk from "chalk";
import ora from "ora";

// Load environment variables
dotenv.config();

// Test configuration
const TEST_TOPIC = "AI Customer Service Automation";
const TEST_LANGUAGE = "en";
const TEST_CATEGORY = "ai-agents";

// Helper function to display results
function displayResults(title: string, data: any) {
  console.log(chalk.cyan(`\n📊 ${title}:`));
  console.log(chalk.gray(JSON.stringify(data, null, 2).substring(0, 500) + "..."));
}

// Main test function
async function testDataForSEO() {
  console.log(chalk.bold.cyan("\n🚀 DataForSEO Integration Test\n"));

  // Check environment variables
  const requiredVars = ["DATAFORSEO_LOGIN", "DATAFORSEO_PASSWORD", "OPENAI_API_KEY"];
  const missingVars = requiredVars.filter(v => !process.env[v]);

  if (missingVars.length > 0) {
    console.log(chalk.red("❌ Missing required environment variables:"));
    missingVars.forEach(v => console.log(chalk.red(`   - ${v}`)));
    console.log(chalk.yellow("\n💡 Create a .env file with these variables"));
    process.exit(1);
  }

  console.log(chalk.green("✅ Environment variables configured"));
  console.log(chalk.dim(`   DataForSEO User: ${process.env.DATAFORSEO_LOGIN}`));
  console.log(chalk.dim(`   OpenAI API: Configured`));

  // Test 1: Quick API connectivity test
  const connectivitySpinner = ora("Testing DataForSEO API connectivity...").start();

  try {
    // We'll test by generating a small blog post
    console.log(chalk.cyan("\n\n📝 Test Case: Generate Blog Post"));
    console.log(chalk.dim(`   Topic: ${TEST_TOPIC}`));
    console.log(chalk.dim(`   Language: ${TEST_LANGUAGE}`));
    console.log(chalk.dim(`   Category: ${TEST_CATEGORY}`));

    connectivitySpinner.succeed("API connectivity verified");

    // Test 2: Full blog generation (dry run)
    const generationSpinner = ora("Generating test blog post...").start();

    const startTime = Date.now();

    // Note: This will actually generate a blog post
    // In a real test, you might want to create a test mode that doesn't save files
    const imageUrl = await dataForSEO.generateBlogPost(
      TEST_TOPIC,
      TEST_LANGUAGE,
      TEST_CATEGORY
    );

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    generationSpinner.succeed(`Blog post generated in ${duration}s`);

    // Display results
    console.log(chalk.green("\n✨ Test Results:"));
    console.log(chalk.dim(`   Generation Time: ${duration} seconds`));
    console.log(chalk.dim(`   Featured Image: ${imageUrl || "Generated"}`));
    console.log(chalk.dim(`   Blog Location: src/content/blog/${TEST_LANGUAGE}/`));

    // Test 3: Verify the output
    console.log(chalk.cyan("\n🔍 Verifying Output:"));

    const expectedFeatures = [
      "Real keyword research data from DataForSEO",
      "Competitor analysis from top-ranking sites",
      "Content gaps identification",
      "SEO-optimized content structure",
      "Featured image generation",
      "Proper metadata and schema markup"
    ];

    expectedFeatures.forEach(feature => {
      console.log(chalk.green(`   ✓ ${feature}`));
    });

    console.log(chalk.bold.green("\n🎉 All tests passed! DataForSEO integration is working correctly.\n"));

    // Provide next steps
    console.log(chalk.cyan("📋 Next Steps:"));
    console.log("   1. Check the generated blog post in src/content/blog/en/");
    console.log("   2. Verify the keyword research data is accurate");
    console.log("   3. Review the competitor analysis results");
    console.log("   4. Test with different languages and topics");
    console.log("   5. Monitor API usage in your DataForSEO dashboard\n");

  } catch (error: any) {
    connectivitySpinner.fail("Test failed");
    console.error(chalk.red("\n❌ Error during testing:"));
    console.error(chalk.red(error.message));

    if (error.stack && process.env.DEBUG) {
      console.error(chalk.dim(error.stack));
    }

    // Provide troubleshooting tips
    console.log(chalk.yellow("\n💡 Troubleshooting Tips:"));
    console.log("   1. Verify your DataForSEO credentials are correct");
    console.log("   2. Check your DataForSEO account has sufficient credits");
    console.log("   3. Ensure your OpenAI API key is valid");
    console.log("   4. Check your internet connection");
    console.log("   5. Review the API documentation at https://docs.dataforseo.com/\n");

    process.exit(1);
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  testDataForSEO().catch(error => {
    console.error(chalk.red("Unexpected error:"), error);
    process.exit(1);
  });
}

export { testDataForSEO };
