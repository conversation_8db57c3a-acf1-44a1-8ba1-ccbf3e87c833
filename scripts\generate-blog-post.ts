#!/usr/bin/env node

/**
 * Blog Post Generation Script
 * This script uses DataForSEO and AI to generate SEO-optimized blog posts
 *
 * Usage:
 * npm run generate:blog -- --topic "AI Agents for Customer Service" --lang en --category ai-agents
 *
 * Or with all languages:
 * npm run generate:blog -- --topic "AI Agents for Customer Service" --all-languages
 */

import * as dotenv from "dotenv";
import { dataForSEO } from "../src/utils/dataforseo";
import { program } from "commander";
import chalk from "chalk";
import ora from "ora";
import prompts from "prompts";

// Load environment variables from .env file
dotenv.config();

// Blog post topics suggestions
const topicSuggestions = {
  "ai-agents": [
    "How AI Agents Transform Customer Service",
    "Multi-Agent Systems for Enterprise Automation",
    "Building Custom AI Agents for Your Business",
    "AI Agent Orchestration Best Practices",
    "ROI of Implementing AI Agents",
  ],
  automation: [
    "Intelligent Process Automation vs RPA",
    "Automating Sales Workflows with AI",
    "Document Processing Automation Guide",
    "Email Automation with AI Agents",
    "Workflow Automation Case Studies",
  ],
  "case-studies": [
    "How Company X Reduced Costs 40% with AI Agents",
    "E-commerce Automation Success Story",
    "AI Customer Support Implementation",
    "Manufacturing Process Optimization with AI",
    "Healthcare Administration Automation",
  ],
  tutorials: [
    "Getting Started with AI Agents",
    "Implementing Your First AI Agent",
    "AI Agent Integration Guide",
    "Monitoring AI Agent Performance",
    "Scaling AI Agent Infrastructure",
  ],
  "industry-insights": [
    "Future of AI Agents in 2024",
    "AI Agents Market Analysis",
    "Ethical Considerations in AI Automation",
    "AI Agents vs Traditional Software",
    "Industry-Specific AI Agent Applications",
  ],
};

// Configure command line interface
program
  .name("generate-blog-post")
  .description("Generate SEO-optimized blog posts using AI")
  .version("1.0.0")
  .option("-t, --topic <topic>", "Blog post topic")
  .option("-l, --lang <language>", "Language (en, es, it)", "en")
  .option("-c, --category <category>", "Blog category")
  .option("--all-languages", "Generate for all languages")
  .option("--interactive", "Interactive mode")
  .option("--dry-run", "Show what would be generated without creating files")
  .option("--single-research", "Use English keyword research for all languages")
  .option(
    "--review-keywords",
    "Force interactive keyword selection for quality control",
  )
  .parse();

const options = program.opts();

// Validate language
const validLanguages = ["en", "es", "it"] as const;
type Language = (typeof validLanguages)[number];

function isValidLanguage(lang: string): lang is Language {
  return validLanguages.includes(lang as Language);
}

// Validate category
const validCategories = [
  "ai-agents",
  "automation",
  "case-studies",
  "tutorials",
  "industry-insights",
  "product-updates",
  "best-practices",
] as const;

type Category = (typeof validCategories)[number];

function isValidCategory(cat: string): cat is Category {
  return validCategories.includes(cat as Category);
}

// Interactive mode
async function interactiveMode() {
  console.log(chalk.cyan("\n🤖 AI Blog Post Generator - Interactive Mode\n"));

  // Select category
  const categoryResponse = await prompts({
    type: "select",
    name: "category",
    message: "Select blog category:",
    choices: validCategories.map((cat) => ({
      title: cat.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase()),
      value: cat,
    })),
  });

  if (!categoryResponse.category) {
    console.log(chalk.red("❌ Category selection cancelled"));
    process.exit(1);
  }

  // Show topic suggestions
  const suggestions =
    topicSuggestions[
      categoryResponse.category as keyof typeof topicSuggestions
    ] || [];

  const topicResponse = await prompts({
    type: "autocomplete",
    name: "topic",
    message: "Enter blog post topic:",
    choices: [
      ...suggestions.map((s) => ({ title: s, value: s })),
      { title: "Custom topic...", value: "custom" },
    ],
  });

  let topic = topicResponse.topic;

  if (topic === "custom") {
    const customTopicResponse = await prompts({
      type: "text",
      name: "topic",
      message: "Enter your custom topic:",
      validate: (value) =>
        value.length > 10 || "Topic must be at least 10 characters",
    });
    topic = customTopicResponse.topic;
  }

  if (!topic) {
    console.log(chalk.red("❌ Topic selection cancelled"));
    process.exit(1);
  }

  // Select languages
  const languageResponse = await prompts({
    type: "multiselect",
    name: "languages",
    message: "Select languages to generate:",
    choices: [
      { title: "English", value: "en", selected: true },
      { title: "Español", value: "es" },
      { title: "Italiano", value: "it" },
    ],
    min: 1,
  });

  if (!languageResponse.languages || languageResponse.languages.length === 0) {
    console.log(chalk.red("❌ Language selection cancelled"));
    process.exit(1);
  }

  // Ask about single research mode if multiple languages selected
  let singleResearch = false;
  if (languageResponse.languages.length > 1) {
    const researchModeResponse = await prompts({
      type: "confirm",
      name: "singleResearch",
      message:
        "Use English keyword research for all languages? (saves API calls)",
      initial: true,
    });
    singleResearch = researchModeResponse.singleResearch || false;
  }

  return {
    topic,
    category: categoryResponse.category as Category,
    languages: languageResponse.languages as Language[],
    singleResearch,
  };
}

// Main generation function
async function generateBlogPost(
  topic: string,
  language: Language,
  category: Category,
  dryRun: boolean = false,
  researchLanguage?: Language,
  sharedImageUrl?: string,
  reviewKeywords: boolean = false,
): Promise<string | undefined> {
  const spinner = ora({
    text: `Generating blog post: "${topic}" (${language})`,
    color: "cyan",
  }).start();

  try {
    if (dryRun) {
      spinner.info(
        `[DRY RUN] Would generate: ${topic} (${language}) in category: ${category}`,
      );

      // Simulate the process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      spinner.succeed(
        `[DRY RUN] Blog post would be saved to: src/content/blog/${language}/${topic.toLowerCase().replace(/\s+/g, "-")}.md`,
      );
      return undefined;
    }

    // Update spinner with progress
    spinner.text = `Performing keyword research for "${topic}" (${language})...`;
    await new Promise((resolve) => setTimeout(resolve, 1000));

    spinner.text = `Analyzing competitors for "${topic}" (${language})...`;
    await new Promise((resolve) => setTimeout(resolve, 1000));

    spinner.text = `Generating content structure for "${topic}" (${language})...`;
    await new Promise((resolve) => setTimeout(resolve, 1000));

    spinner.text = `Creating content with AI for "${topic}" (${language})...`;
    await new Promise((resolve) => setTimeout(resolve, 2000));

    spinner.text = `Humanizing and optimizing content for "${topic}" (${language})...`;
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Generate the blog post
    const imageUrl = await dataForSEO.generateBlogPost(
      topic,
      language,
      category,
      researchLanguage,
      sharedImageUrl,
      reviewKeywords,
    );

    spinner.succeed(
      chalk.green(
        `✅ Successfully generated blog post: "${topic}" (${language})`,
      ),
    );

    // Show additional info
    console.log(chalk.dim(`   📁 Location: src/content/blog/${language}/`));
    console.log(chalk.dim(`   🏷️  Category: ${category}`));
    console.log(
      chalk.dim(`   🔍 SEO optimized with primary and long-tail keywords`),
    );
    console.log(chalk.dim(`   🔗 Internal links added automatically`));

    return imageUrl;
  } catch (error) {
    spinner.fail(
      chalk.red(`❌ Failed to generate blog post: ${error.message}`),
    );
    throw error;
  }
}

// Main execution
async function main() {
  try {
    let topic: string;
    let category: Category;
    let languages: Language[];
    let singleResearch: boolean = false;

    if (options.interactive) {
      // Interactive mode
      const selections = await interactiveMode();
      topic = selections.topic;
      category = selections.category;
      languages = selections.languages;
      singleResearch = selections.singleResearch;
    } else {
      // Command line mode
      if (!options.topic) {
        console.log(chalk.red("❌ Error: Topic is required"));
        console.log(chalk.yellow("💡 Tip: Use --interactive for guided setup"));
        program.help();
      }

      topic = options.topic;

      // Validate language
      if (!isValidLanguage(options.lang)) {
        console.log(chalk.red(`❌ Error: Invalid language "${options.lang}"`));
        console.log(
          chalk.yellow(`Valid languages: ${validLanguages.join(", ")}`),
        );
        process.exit(1);
      }

      // Validate category
      if (!options.category) {
        console.log(chalk.red("❌ Error: Category is required"));
        console.log(
          chalk.yellow(`Valid categories: ${validCategories.join(", ")}`),
        );
        process.exit(1);
      }

      if (!isValidCategory(options.category)) {
        console.log(
          chalk.red(`❌ Error: Invalid category "${options.category}"`),
        );
        console.log(
          chalk.yellow(`Valid categories: ${validCategories.join(", ")}`),
        );
        process.exit(1);
      }

      category = options.category as Category;
      languages = options.allLanguages
        ? [...validLanguages]
        : [options.lang as Language];
    }

    // Show generation plan
    console.log(chalk.cyan("\n📋 Generation Plan:\n"));
    console.log(`   Topic: ${chalk.bold(topic)}`);
    console.log(`   Category: ${chalk.bold(category)}`);
    console.log(`   Languages: ${chalk.bold(languages.join(", "))}`);
    console.log(
      `   Mode: ${chalk.bold(options.dryRun ? "Dry Run" : "Production")}`,
    );
    if (options.reviewKeywords || options.interactive) {
      console.log(
        `   Keyword Review: ${chalk.bold("Enabled - You will select keywords interactively")}`,
      );
    }
    console.log("");

    if ((options.singleResearch || singleResearch) && languages.length > 1) {
      console.log(
        `   Research Mode: ${chalk.bold("Single keyword research (English)")}\n`,
      );
    }

    // Confirm if not in dry run mode
    if (!options.dryRun && !options.interactive) {
      const confirmResponse = await prompts({
        type: "confirm",
        name: "confirm",
        message: "Do you want to proceed with generation?",
        initial: true,
      });

      if (!confirmResponse.confirm) {
        console.log(chalk.yellow("⚠️  Generation cancelled"));
        process.exit(0);
      }
    }

    // Generate for each language
    console.log(chalk.cyan("\n🚀 Starting generation...\n"));

    // If single research mode is enabled, use English for keyword research
    const researchLang =
      options.singleResearch || singleResearch ? "en" : undefined;

    if ((options.singleResearch || singleResearch) && languages.length > 1) {
      console.log(
        chalk.yellow("📊 Using English keyword research for all languages\n"),
      );
    }

    // Track shared image URL across languages
    let sharedImageUrl: string | undefined;

    for (const lang of languages) {
      const imageUrl = await generateBlogPost(
        topic,
        lang,
        category,
        options.dryRun,
        researchLang,
        sharedImageUrl,
        (options.reviewKeywords || options.interactive) &&
          (!researchLang || languages.indexOf(lang) === 0),
      );

      // Use the image from the first language for all subsequent ones
      if (!sharedImageUrl && imageUrl) {
        sharedImageUrl = imageUrl;
        if (languages.length > 1) {
          console.log(
            chalk.green(
              "\n🎨 Featured image generated - will be reused for all languages",
            ),
          );
        }
      }

      // Small delay between generations
      if (languages.indexOf(lang) < languages.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Summary
    console.log(chalk.green("\n✨ Generation complete!\n"));

    // Highlight DataForSEO integration
    console.log(
      chalk.bold.cyan("🚀 Powered by Real DataForSEO Intelligence!\n"),
    );
    console.log(
      chalk.dim("   • Real-time keyword data from 70M+ keywords database"),
    );
    console.log(
      chalk.dim("   • Live competitor analysis from actual SERP results"),
    );
    console.log(
      chalk.dim("   • Search intent classification for better targeting"),
    );
    console.log(
      chalk.dim("   • Content gaps identified from top-ranking pages\n"),
    );

    if (!options.dryRun) {
      console.log(chalk.cyan("📝 Next steps:"));
      console.log("   1. Review the generated content in src/content/blog/");
      console.log("   2. Add featured images if needed");
      console.log("   3. Run `npm run build` to see the posts on your site");
      console.log("   4. Monitor performance with your analytics tools\n");
    }
  } catch (error) {
    console.error(chalk.red("\n❌ Generation failed:"), error.message);

    if (error.stack && process.env.DEBUG) {
      console.error(chalk.dim(error.stack));
    }

    process.exit(1);
  }
}

// Check for required environment variables
function checkEnvironment() {
  const required = ["DATAFORSEO_LOGIN", "DATAFORSEO_PASSWORD", "O3_API_KEY"];

  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    console.log(chalk.red("❌ Missing required environment variables:"));
    missing.forEach((key) => console.log(chalk.red(`   - ${key}`)));
    console.log(
      chalk.yellow(
        "\n💡 Create a .env file with these variables or set them in your environment",
      ),
    );
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  checkEnvironment();
  main().catch((error) => {
    console.error(chalk.red("Unexpected error:"), error);
    process.exit(1);
  });
}

export { generateBlogPost, main };
