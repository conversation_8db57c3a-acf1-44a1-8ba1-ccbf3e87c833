---
import { getLangFromAstro, useTranslations, localizePath } from '../i18n/utils';

const lang = getLangFromAstro(Astro);
const t = useTranslations(lang);
// CTA Section component
---

<section id="cta" class="container cta-section">
    <h2 class="section-title">{t("cta.title")}</h2>
    <p class="section-subtitle">{t("cta.subtitle")}</p>
    <a href={localizePath("/contact", lang)} class="cta-button">{t("cta.button")}</a>
</section>
