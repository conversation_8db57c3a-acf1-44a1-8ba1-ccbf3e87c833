/**
 * DataForSEO Integration - Professional Growth Hacking Tool
 *
 * This module implements advanced SEO research and content generation using
 * DataForSEO Labs API for real-time competitive intelligence and keyword insights.
 *
 * <AUTHOR> Growth Hacker with <PERSON> vision and <PERSON> skills
 */

import * as client from "dataforseo-client";
import OpenAI from "openai";
import * as fs from "fs/promises";
import * as path from "path";
import { fileURLToPath } from "url";
import prompts from "prompts";
import chalk from "chalk";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration interfaces
interface DataForSEOConfig {
  username: string;
  password: string;
  language_code: string;
  location_code: number;
}

interface KeywordData {
  keyword: string;
  search_volume: number;
  competition: number;
  cpc: number;
  trend: number;
  keyword_difficulty: number;
  search_intent: string[];
  monthly_searches: Array<{
    year: number;
    month: number;
    search_volume: number;
  }>;
}

interface BlogPostMetadata {
  title: string;
  description: string;
  keywords: string[];
  author: string;
  category: string;
  tags: string[];
  readingTime: number;
  featuredImage?: string;
  featuredImageAlt?: string;
  publishDate: string;
  lastModified: string;
  schema: {
    type: string;
    headline: string;
    description: string;
    image?: string;
    datePublished: string;
    dateModified: string;
    author: {
      type: string;
      name: string;
    };
  };
  internal_links?: Array<{
    url: string;
    text: string;
    context: string;
  }>;
}

interface CompetitorData {
  domain: string;
  title: string;
  url: string;
  metrics: {
    domain_rank: number;
    page_rank: number;
    backlinks: number;
    traffic: number;
  };
  content_insights: {
    word_count: number;
    headings: string[];
    keywords_density: Record<string, number>;
  };
}

interface ContentStrategy {
  primary_keyword: string;
  secondary_keywords: string[];
  long_tail_keywords: string[];
  questions: string[];
  search_intent: string;
  content_gaps: string[];
  recommended_structure: {
    title: string;
    sections: Array<{
      heading: string;
      keywords: string[];
      intent: string;
      word_count: number;
    }>;
  };
}

/**
 * DataForSEO Professional Integration
 * Implements real-time SEO intelligence for content optimization
 */
export class DataForSEO {
  private labsApi: client.DataforseoLabsApi | null = null;
  private keywordsApi: client.KeywordsDataApi | null = null;
  private openai: OpenAI | null = null;
  private authFetch: any = null;
  private config: DataForSEOConfig;
  private contentDir: string;

  constructor() {
    // Lazy initialization to ensure env vars are loaded
    this.config = {
      username: "",
      password: "",
      language_code: "en",
      location_code: 2840, // USA
    };
    this.contentDir = path.join(process.cwd(), "src", "content", "blog");
  }

  /**
   * Create authenticated fetch function for DataForSEO
   */
  private createAuthenticatedFetch(username: string, password: string) {
    return (url: RequestInfo, init?: RequestInit): Promise<Response> => {
      const token = Buffer.from(`${username}:${password}`).toString("base64");
      const authHeader = { Authorization: `Basic ${token}` };

      const newInit: RequestInit = {
        ...init,
        headers: {
          ...init?.headers,
          ...authHeader,
        },
      };

      return fetch(url, newInit);
    };
  }

  /**
   * Initialize clients with lazy loading
   */
  private async initializeClients() {
    if (!this.authFetch) {
      this.config.username = process.env.DATAFORSEO_LOGIN || "";
      this.config.password = process.env.DATAFORSEO_PASSWORD || "";

      if (!this.config.username || !this.config.password) {
        throw new Error(
          "DataForSEO credentials not configured. Set DATAFORSEO_LOGIN and DATAFORSEO_PASSWORD",
        );
      }

      this.authFetch = this.createAuthenticatedFetch(
        this.config.username,
        this.config.password,
      );
    }

    if (!this.labsApi) {
      this.labsApi = new client.DataforseoLabsApi(
        "https://api.dataforseo.com",
        { fetch: this.authFetch },
      );
    }

    if (!this.keywordsApi) {
      this.keywordsApi = new client.KeywordsDataApi(
        "https://api.dataforseo.com",
        { fetch: this.authFetch },
      );
    }

    if (!this.openai) {
      const openaiKey = process.env.OPENAI_API_KEY;
      if (!openaiKey) {
        throw new Error("OpenAI API key not configured. Set OPENAI_API_KEY");
      }
      this.openai = new OpenAI({ apiKey: openaiKey });
    }
  }

  /**
   * Main blog post generation method with real SEO data
   */
  async generateBlogPost(
    topic: string,
    language: "en" | "es" | "it" = "en",
    category: string = "ai-agents",
    researchLanguage?: "en" | "es" | "it",
    sharedImageUrl?: string,
    reviewKeywords: boolean = false,
  ): Promise<string | undefined> {
    await this.initializeClients();

    try {
      // Step 1: Perform comprehensive keyword research
      console.log(`🔍 Performing keyword research for: ${topic}`);
      const keywordStrategy = await this.performKeywordResearch(
        topic,
        researchLanguage || language,
        reviewKeywords,
      );

      // Step 2: Analyze top competitors
      console.log(`🏆 Analyzing competitors...`);
      const competitors = await this.analyzeCompetitors(
        keywordStrategy.primary_keyword,
        language,
      );

      // Step 3: Identify content opportunities
      console.log(`💡 Identifying content gaps...`);
      const contentGaps = await this.identifyContentGaps(
        keywordStrategy,
        competitors,
      );

      // Step 4: Generate optimized content
      console.log(`✍️ Generating SEO-optimized content...`);
      const content = await this.generateOptimizedContent(
        topic,
        keywordStrategy,
        contentGaps,
        language,
      );

      // Step 5: Generate or reuse featured image
      let imageUrl = sharedImageUrl;
      if (!imageUrl) {
        console.log(`🎨 Generating featured image...`);
        imageUrl = await this.generateFeaturedImage(
          topic,
          keywordStrategy.primary_keyword,
        );
      }

      // Step 6: Save the blog post
      console.log(`💾 Saving blog post...`);
      await this.saveBlogPost(content, language, category, imageUrl);

      return imageUrl;
    } catch (error) {
      console.error("Error generating blog post:", error);
      throw error;
    }
  }

  /**
   * Perform comprehensive keyword research using DataForSEO Labs
   */
  private async performKeywordResearch(
    topic: string,
    language: string,
    reviewKeywords: boolean = false,
  ): Promise<ContentStrategy> {
    if (!this.labsApi) throw new Error("Labs API not initialized");

    const locationCode = this.getLocationCode(language);

    // Create keyword ideas request
    const keywordIdeasRequest =
      new client.DataforseoLabsGoogleKeywordIdeasLiveRequestInfo();
    keywordIdeasRequest.keywords = [topic];
    keywordIdeasRequest.location_code = locationCode;
    keywordIdeasRequest.language_code = language;
    keywordIdeasRequest.include_seed_keyword = true;
    keywordIdeasRequest.limit = 100;

    // Create related keywords request
    const relatedKeywordsRequest =
      new client.DataforseoLabsGoogleRelatedKeywordsLiveRequestInfo();
    relatedKeywordsRequest.keyword = topic;
    relatedKeywordsRequest.location_code = locationCode;
    relatedKeywordsRequest.language_code = language;
    relatedKeywordsRequest.limit = 50;

    // Create keyword suggestions request
    const keywordSuggestionsRequest =
      new client.DataforseoLabsGoogleKeywordSuggestionsLiveRequestInfo();
    keywordSuggestionsRequest.keyword = topic;
    keywordSuggestionsRequest.location_code = locationCode;
    keywordSuggestionsRequest.language_code = language;
    keywordSuggestionsRequest.limit = 20;

    // Execute all requests
    const [
      keywordIdeasResponse,
      relatedKeywordsResponse,
      keywordSuggestionsResponse,
    ] = await Promise.all([
      this.labsApi.googleKeywordIdeasLive([keywordIdeasRequest]),
      this.labsApi.googleRelatedKeywordsLive([relatedKeywordsRequest]),
      this.labsApi.googleKeywordSuggestionsLive([keywordSuggestionsRequest]),
    ]);

    // Process and analyze keyword data
    const keywordData = this.processKeywordData(
      keywordIdeasResponse,
      relatedKeywordsResponse,
      keywordSuggestionsResponse,
    );

    // Build content strategy with interactive selection
    return await this.buildContentStrategy(topic, keywordData, reviewKeywords);
  }

  /**
   * Analyze top-ranking competitors
   */
  private async analyzeCompetitors(
    keyword: string,
    language: string,
  ): Promise<CompetitorData[]> {
    if (!this.labsApi) throw new Error("Labs API not initialized");

    const locationCode = this.getLocationCode(language);

    // Create SERP competitors request
    const serpCompetitorsRequest =
      new client.DataforseoLabsGoogleSerpCompetitorsLiveRequestInfo();
    serpCompetitorsRequest.keywords = [keyword];
    serpCompetitorsRequest.location_code = locationCode;
    serpCompetitorsRequest.language_code = language;
    serpCompetitorsRequest.limit = 10;

    const serpCompetitorsResponse =
      await this.labsApi.googleSerpCompetitorsLive([serpCompetitorsRequest]);

    // Process competitor data
    const competitors: CompetitorData[] = [];

    if (serpCompetitorsResponse?.tasks?.[0]?.result?.[0]?.items) {
      for (const item of serpCompetitorsResponse.tasks[0].result[0].items.slice(
        0,
        5,
      )) {
        const competitorData: CompetitorData = {
          domain: item.domain || "",
          title: item.title || "",
          url: item.url || "",
          metrics: {
            domain_rank: item.avg_position || 0,
            page_rank: 0,
            backlinks: 0,
            traffic: item.etv || 0,
          },
          content_insights: {
            word_count: 0,
            headings: [],
            keywords_density: {},
          },
        };

        // Get more insights about competitor content
        try {
          const rankedKeywordsRequest =
            new client.DataforseoLabsGoogleRankedKeywordsLiveRequestInfo();
          rankedKeywordsRequest.target = item.domain;
          rankedKeywordsRequest.location_code = locationCode;
          rankedKeywordsRequest.language_code = language;
          rankedKeywordsRequest.limit = 20;

          const rankedKeywordsResponse =
            await this.labsApi.googleRankedKeywordsLive([
              rankedKeywordsRequest,
            ]);

          if (rankedKeywordsResponse?.tasks?.[0]?.result?.[0]?.items) {
            const keywords = rankedKeywordsResponse.tasks[0].result[0].items;
            competitorData.content_insights.keywords_density = keywords.reduce(
              (acc: any, kw: any) => {
                if (kw.keyword_data?.keyword) {
                  acc[kw.keyword_data.keyword] =
                    kw.keyword_data?.search_volume || 0;
                }
                return acc;
              },
              {},
            );
          }
        } catch (error) {
          console.warn(
            `Could not get ranked keywords for ${item.domain}:`,
            error,
          );
        }

        competitors.push(competitorData);
      }
    }

    return competitors;
  }

  /**
   * Identify content gaps and opportunities
   */
  private async identifyContentGaps(
    strategy: ContentStrategy,
    competitors: CompetitorData[],
  ): Promise<string[]> {
    if (!this.labsApi) throw new Error("Labs API not initialized");

    const gaps: Set<string> = new Set();

    // Analyze competitor keywords we're not targeting
    const ourKeywords = new Set([
      strategy.primary_keyword,
      ...strategy.secondary_keywords,
      ...strategy.long_tail_keywords,
    ]);

    competitors.forEach((competitor) => {
      Object.keys(competitor.content_insights.keywords_density).forEach(
        (keyword) => {
          if (!ourKeywords.has(keyword) && keyword.split(" ").length > 2) {
            gaps.add(keyword);
          }
        },
      );
    });

    // Get additional gap insights from relevant pages
    try {
      const relevantPagesRequest =
        new client.DataforseoLabsGoogleRelevantPagesLiveRequestInfo();
      relevantPagesRequest.target = strategy.primary_keyword;
      relevantPagesRequest.location_code = this.getLocationCode("en");
      relevantPagesRequest.language_code = "en";
      relevantPagesRequest.limit = 10;

      const relevantPagesResponse = await this.labsApi.googleRelevantPagesLive([
        relevantPagesRequest,
      ]);

      if (relevantPagesResponse?.tasks?.[0]?.result?.[0]?.items) {
        relevantPagesResponse.tasks[0].result[0].items.forEach((item: any) => {
          if (item.meta_keywords) {
            item.meta_keywords.forEach((kw: string) => {
              if (!ourKeywords.has(kw)) {
                gaps.add(kw);
              }
            });
          }
        });
      }
    } catch (error) {
      console.warn("Could not get relevant pages:", error);
    }

    return Array.from(gaps).slice(0, 10);
  }

  /**
   * Generate SEO-optimized content using AI
   */
  private async generateOptimizedContent(
    topic: string,
    strategy: ContentStrategy,
    contentGaps: string[],
    language: string,
  ): Promise<{ metadata: BlogPostMetadata; content: string }> {
    if (!this.openai) throw new Error("OpenAI not initialized");

    // Create comprehensive content brief
    const contentBrief = this.createContentBrief(
      topic,
      strategy,
      contentGaps,
      language,
    );

    // Generate content with AI
    const contentResponse = await this.openai.chat.completions.create({
      model: "gpt-4-turbo-preview",
      messages: [
        {
          role: "system",
          content: this.getContentGenerationPrompt(language),
        },
        {
          role: "user",
          content: contentBrief,
        },
      ],
      temperature: 0.7,
      max_tokens: 4000,
    });

    const rawContent = contentResponse.choices[0]?.message?.content || "";

    // Generate metadata
    const metadata = await this.generateMetadata(
      topic,
      strategy,
      rawContent,
      language,
    );

    // Optimize content with internal links and SEO enhancements
    const optimizedContent = await this.optimizeContent(
      rawContent,
      metadata,
      language,
    );

    return { metadata, content: optimizedContent };
  }

  /**
   * Filter keywords for relevance using AI
   */
  private async filterRelevantKeywords(
    topic: string,
    keywords: KeywordData[],
  ): Promise<KeywordData[]> {
    if (!this.openai) return keywords; // Fallback if no AI

    console.log(chalk.yellow("\n🔍 Filtering keywords for relevance..."));

    // Batch keywords for efficiency
    const batches: KeywordData[][] = [];
    for (let i = 0; i < keywords.length; i += 50) {
      batches.push(keywords.slice(i, i + 50));
    }

    const relevantKeywords: KeywordData[] = [];

    for (const batch of batches) {
      const keywordList = batch.map((k: KeywordData) => k.keyword).join("\n");

      try {
        const response = await this.openai.chat.completions.create({
          model: "gpt-4-turbo-preview",
          messages: [
            {
              role: "system",
              content: `You are a strict keyword relevance filter. Your job is to identify keywords that are DIRECTLY related to the given topic.

BALANCED RULES:
1. Keep keywords that are related to the main concept
2. Remove location-specific keywords only if completely unrelated
3. Remove celebrity names only if clearly unrelated
4. Keep keywords that could be used in the same content
5. Include variations and related concepts
6. When in doubt, INCLUDE the keyword

Return a JSON array of keywords that passed the filter.`,
            },
            {
              role: "user",
              content: `Topic: "${topic}"

Keywords to filter:
${keywordList}

Return ONLY keywords that are DIRECTLY about "${topic}". Be extremely strict.`,
            },
          ],
          temperature: 0,
          response_format: { type: "json_object" },
        });

        const result = JSON.parse(
          response.choices[0]?.message?.content || "{}",
        );
        const approvedKeywords = new Set(result.keywords || []);

        // Add only approved keywords
        batch.forEach((kw: KeywordData) => {
          if (approvedKeywords.has(kw.keyword)) {
            relevantKeywords.push(kw);
          }
        });
      } catch (error) {
        console.warn(
          "Relevance filtering failed, keeping all keywords:",
          error,
        );
        relevantKeywords.push(...batch);
      }
    }

    console.log(
      chalk.green(
        `✓ Filtered ${keywords.length} keywords down to ${relevantKeywords.length} relevant ones\n`,
      ),
    );
    return relevantKeywords;
  }

  /**
   * Process keyword data from multiple sources
   */
  private processKeywordData(
    keywordIdeas: any,
    relatedKeywords: any,
    keywordSuggestions: any,
  ): KeywordData[] {
    const keywords: KeywordData[] = [];
    const seen = new Set<string>();

    // Process keyword ideas
    if (keywordIdeas?.tasks?.[0]?.result?.[0]?.items) {
      keywordIdeas.tasks[0].result[0].items.forEach((item: any) => {
        if (!seen.has(item.keyword) && item.keyword_info) {
          seen.add(item.keyword);
          keywords.push({
            keyword: item.keyword,
            search_volume: item.keyword_info.search_volume || 0,
            competition: item.keyword_info.competition || 0,
            cpc: item.keyword_info.cpc || 0,
            trend: item.keyword_info.trend || 0,
            keyword_difficulty: item.keyword_difficulty || 0,
            search_intent: item.search_intent || [],
            monthly_searches: item.keyword_info.monthly_searches || [],
          });
        }
      });
    }

    // Process related keywords
    if (relatedKeywords?.tasks?.[0]?.result?.[0]?.items) {
      relatedKeywords.tasks[0].result[0].items.forEach((item: any) => {
        if (!seen.has(item.keyword_data?.keyword) && item.keyword_data) {
          const kw = item.keyword_data.keyword;
          seen.add(kw);
          keywords.push({
            keyword: kw,
            search_volume: item.keyword_data.search_volume || 0,
            competition: item.keyword_data.competition || 0,
            cpc: item.keyword_data.cpc || 0,
            trend: item.keyword_data.trend || 0,
            keyword_difficulty: item.keyword_data.keyword_difficulty || 0,
            search_intent: [],
            monthly_searches: item.keyword_data.monthly_searches || [],
          });
        }
      });
    }

    // Process suggestions (questions)
    if (keywordSuggestions?.tasks?.[0]?.result?.[0]?.items) {
      keywordSuggestions.tasks[0].result[0].items.forEach((item: any) => {
        if (!seen.has(item.keyword) && item.keyword.includes("?")) {
          seen.add(item.keyword);
          keywords.push({
            keyword: item.keyword,
            search_volume: item.keyword_data?.search_volume || 0,
            competition: 0,
            cpc: 0,
            trend: 0,
            keyword_difficulty: 0,
            search_intent: ["informational"],
            monthly_searches: [],
          });
        }
      });
    }

    return keywords;
  }

  /**
   * Filter out irrelevant keywords
   */
  private isRelevantKeyword(keyword: string): boolean {
    const lowerKeyword = keyword.toLowerCase();

    // Blacklist of irrelevant terms
    const blacklist = [
      // Celebrity names
      "doctor dre",
      "dr dre",
      "eminem",
      "beyonce",
      "taylor swift",
      // Specific locations (unless explicitly requested)
      "fairfield ct",
      "connecticut",
      "new york",
      "los angeles",
      // Medical terms unrelated to AI
      "eye doctor",
      "dentist",
      "surgeon",
      "hospital",
      "clinic",
      // Personal/casual terms
      "my doctor",
      "near me",
      "wife",
      "husband",
      "girlfriend",
      // Brand names unrelated to AI
      "iphone",
      "samsung",
      "coca cola",
      "mcdonalds",
    ];

    // Check blacklist
    for (const term of blacklist) {
      if (lowerKeyword.includes(term)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Build a comprehensive content strategy
   */
  private async buildContentStrategy(
    topic: string,
    keywords: KeywordData[],
    forceReview: boolean = false,
  ): Promise<ContentStrategy> {
    // Use ALL keywords - let the user decide what's relevant
    const relevantKeywords = keywords;

    if (relevantKeywords.length === 0) {
      throw new Error(
        `No keywords found for topic: ${topic}. DataForSEO returned no results.`,
      );
    }

    // Sort keywords by search volume and difficulty balance
    const scoredKeywords = relevantKeywords
      .map((kw) => ({
        ...kw,
        score:
          (kw.search_volume / (kw.keyword_difficulty + 1)) *
          (1 - kw.competition),
      }))
      .sort((a, b) => b.score - a.score);

    // Interactive keyword selection (when forced or no good match found)
    let primary: (typeof scoredKeywords)[0];
    let secondary: string[];

    // Check if we should force review or if the automatic selection is poor
    const autoSelectedPrimary =
      scoredKeywords.find((kw) =>
        kw.keyword.toLowerCase().includes(topic.toLowerCase().split(" ")[0]),
      ) || scoredKeywords[0];

    const needsReview =
      forceReview ||
      !autoSelectedPrimary ||
      autoSelectedPrimary.score < 5 || // More lenient
      autoSelectedPrimary.search_volume < 50; // Lower threshold

    if (needsReview) {
      console.log(
        chalk.cyan("\n🎯 Keyword Analysis Results for:"),
        chalk.bold(topic),
      );
      console.log(chalk.dim("─".repeat(80)));

      // Display top keywords with metrics - show more options
      const topKeywords = scoredKeywords.slice(0, 50);
      const keywordChoices = topKeywords.map((kw, index) => ({
        title: `${kw.keyword} | Vol: ${kw.search_volume.toLocaleString()} | Diff: ${kw.keyword_difficulty} | Intent: ${kw.search_intent.join(", ") || "N/A"} | Trend: ${kw.trend}`,
        value: index,
        description: `Competition: ${(kw.competition * 100).toFixed(0)}% | CPC: $${kw.cpc.toFixed(2)} | Score: ${kw.score.toFixed(2)}`,
      }));

      const primarySelection = await prompts({
        type: "select",
        name: "primary",
        message: "Select PRIMARY keyword for optimization:",
        choices: keywordChoices,
        initial: 0,
      });

      if (primarySelection.primary === undefined) {
        throw new Error("Keyword selection cancelled");
      }

      primary = topKeywords[primarySelection.primary];
      console.log(
        chalk.green(`✓ Primary keyword selected: ${primary.keyword}\n`),
      );

      // Select secondary keywords - show all remaining keywords
      const remainingKeywords = scoredKeywords.filter(
        (_, index) => index !== primarySelection.primary,
      );
      const secondaryChoices = remainingKeywords
        .slice(0, 40) // Show up to 40 options
        .map((kw, index) => ({
          title: `${kw.keyword} | Vol: ${kw.search_volume.toLocaleString()} | Diff: ${kw.keyword_difficulty} | ${kw.trend}`,
          value: kw.keyword,
          selected: index < 10, // Pre-select top 10
        }));

      const secondarySelection = await prompts({
        type: "multiselect",
        name: "secondary",
        message:
          "Select SECONDARY keywords (choose 5-15 for comprehensive coverage):",
        choices: secondaryChoices,
        min: 1,
        max: 15,
        hint: "- Space to select. Enter to confirm. More keywords = better SEO coverage",
      });

      if (
        !secondarySelection.secondary ||
        secondarySelection.secondary.length === 0
      ) {
        throw new Error("Secondary keyword selection cancelled");
      }

      secondary = secondarySelection.secondary;
      console.log(
        chalk.green(`✓ Selected ${secondary.length} secondary keywords\n`),
      );
    } else {
      // Automatic selection - still pick good keywords
      primary = autoSelectedPrimary;
      secondary = scoredKeywords
        .filter((kw) => kw.keyword !== primary.keyword && kw.search_volume > 50)
        .slice(0, 15) // Get more keywords automatically
        .map((kw) => kw.keyword);
    }

    // Select long-tail keywords (lower competition, specific)
    const longTail = scoredKeywords
      .filter(
        (kw) =>
          kw.keyword.split(" ").length >= 3 &&
          kw.competition < 0.5 &&
          !secondary.includes(kw.keyword),
      )
      .slice(0, 30)
      .map((kw) => kw.keyword);

    // Extract questions
    const questions = keywords
      .filter((kw) => kw.keyword.includes("?"))
      .slice(0, 5)
      .map((kw) => kw.keyword);

    // Determine primary search intent
    const intents = keywords
      .flatMap((kw) => kw.search_intent)
      .filter((intent) => intent);
    const intentCounts = intents.reduce((acc: any, intent) => {
      acc[intent] = (acc[intent] || 0) + 1;
      return acc;
    }, {});
    const primaryIntent =
      Object.entries(intentCounts).sort(
        ([, a]: any, [, b]: any) => b - a,
      )[0]?.[0] || "informational";

    // Build content structure based on strategy
    const structure = this.generateContentStructure(
      topic,
      primary.keyword,
      secondary,
      questions,
      primaryIntent,
    );

    return {
      primary_keyword: primary.keyword,
      secondary_keywords: secondary,
      long_tail_keywords: longTail,
      questions,
      search_intent: primaryIntent,
      content_gaps: [],
      recommended_structure: structure,
    };
  }

  /**
   * Generate content structure
   */
  private generateContentStructure(
    topic: string,
    primaryKeyword: string,
    secondaryKeywords: string[],
    questions: string[],
    intent: string,
  ): ContentStrategy["recommended_structure"] {
    const sections: ContentStrategy["recommended_structure"]["sections"] = [];

    // Introduction section
    sections.push({
      heading: "Introduction",
      keywords: [primaryKeyword],
      intent: "informational",
      word_count: 150,
    });

    // Main sections based on secondary keywords
    secondaryKeywords.slice(0, 3).forEach((keyword) => {
      sections.push({
        heading: this.keywordToHeading(keyword),
        keywords: [keyword, primaryKeyword],
        intent: intent,
        word_count: 300,
      });
    });

    // FAQ section if we have questions
    if (questions.length > 0) {
      sections.push({
        heading: "Frequently Asked Questions",
        keywords: questions,
        intent: "informational",
        word_count: 400,
      });
    }

    // Conclusion with CTA
    sections.push({
      heading: "Conclusion",
      keywords: [primaryKeyword],
      intent: "transactional",
      word_count: 150,
    });

    return {
      title: this.generateSEOTitle(topic, primaryKeyword),
      sections,
    };
  }

  /**
   * Convert keyword to heading
   */
  private keywordToHeading(keyword: string): string {
    return keyword
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  /**
   * Generate SEO-optimized title
   */
  private generateSEOTitle(topic: string, primaryKeyword: string): string {
    const year = new Date().getFullYear();
    const templates = [
      `${topic}: The Ultimate Guide (${year})`,
      `How to Master ${topic} with ${primaryKeyword}`,
      `${topic} - Everything You Need to Know`,
      `The Complete ${topic} Strategy for ${year}`,
      `${primaryKeyword}: Professional ${topic} Guide`,
    ];

    return templates[Math.floor(Math.random() * templates.length)];
  }

  /**
   * Get location code for language
   */
  private getLocationCode(language: string): number {
    const locationCodes: Record<string, number> = {
      en: 2840, // United States
      es: 2724, // Spain
      it: 2380, // Italy
    };
    return locationCodes[language] || 2840;
  }

  /**
   * Create content brief for AI
   */
  private createContentBrief(
    topic: string,
    strategy: ContentStrategy,
    contentGaps: string[],
    language: string,
  ): string {
    return `Create a comprehensive, SEO-optimized blog post about "${topic}" in ${language}.

CRITICAL INSTRUCTIONS:
1. The topic is "${topic}" - STAY FOCUSED on this exact topic
2. Do NOT drift into unrelated subtopics
3. Do NOT mention specific locations unless the topic includes them
4. Do NOT include celebrity names or unrelated people
5. Every paragraph must be directly relevant to "${topic}"
6. If a keyword seems unrelated to the main topic, DO NOT use it

SEO Strategy:
- Primary Keyword: ${strategy.primary_keyword}
- Secondary Keywords: ${strategy.secondary_keywords.join(", ")}
- Search Intent: ${strategy.search_intent}
- Topic Focus: "${topic}" ONLY

Content Structure:
${strategy.recommended_structure.sections
  .map(
    (section) =>
      `- ${section.heading} (~${section.word_count} words, keywords: ${section.keywords.slice(0, 2).join(", ")})`,
  )
  .join("\n")}

Questions to Answer:
${strategy.questions.map((q) => `- ${q}`).join("\n")}

Content Gaps to Address:
${contentGaps
  .slice(0, 5)
  .map((gap) => `- ${gap}`)
  .join("\n")}

Requirements:
1. RELEVANCE CHECK: Every sentence must be about "${topic}" - no drift allowed
2. Use natural keyword placement (1-2% density) - but ONLY if relevant
3. Write in a conversational, expert tone about "${topic}" specifically
4. Include actionable insights about "${topic}" only
5. DO NOT mention unrelated locations, people, or services
6. Add CTAs for Agentica.it services related to "${topic}"
7. Format with proper headings (H2, H3) - all about "${topic}"
8. If a keyword doesn't fit naturally with "${topic}", skip it
9. Write approximately ${strategy.recommended_structure.sections.reduce((sum, s) => sum + s.word_count, 0)} words

FINAL CHECK: Before including any sentence, ask "Is this directly about ${topic}?" If no, remove it.

Generate the full blog post content now.`;
  }

  /**
   * Get content generation prompt
   */
  private getContentGenerationPrompt(language: string): string {
    const prompts: Record<string, string> = {
      en: "You are an expert content writer specializing in AI, automation, and growth hacking. Write engaging, informative content that ranks well on Google.",
      es: "Eres un experto redactor de contenido especializado en IA, automatización y growth hacking. Escribe contenido atractivo e informativo que se posicione bien en Google.",
      it: "Sei un esperto scrittore di contenuti specializzato in AI, automazione e growth hacking. Scrivi contenuti coinvolgenti e informativi che si posizionano bene su Google.",
    };
    return prompts[language] || prompts["en"];
  }

  /**
   * Generate metadata for the blog post
   */
  private async generateMetadata(
    topic: string,
    strategy: ContentStrategy,
    content: string,
    language: string,
  ): Promise<BlogPostMetadata> {
    if (!this.openai) throw new Error("OpenAI not initialized");

    const metaResponse = await this.openai.chat.completions.create({
      model: "gpt-4-turbo-preview",
      messages: [
        {
          role: "system",
          content:
            "Generate SEO metadata for blog posts. Be extremely strict about relevance. Return valid JSON only.",
        },
        {
          role: "user",
          content: `Generate metadata for this blog post about "${topic}":

STRICT RULES:
1. Title must be about "${topic}" specifically
2. Description must focus on "${topic}" only
3. Tags must ALL be directly related to "${topic}"
4. Do NOT include location-specific tags unless topic mentions location
5. Do NOT include celebrity/person names unless topic is about them
6. Every tag must pass this test: "Is this tag about ${topic}?" If no, exclude it

Topic: ${topic}
Primary Keyword: ${strategy.primary_keyword}
Language: ${language}
Content Preview: ${content.substring(0, 500)}...

Return JSON with: title (50-60 chars about ${topic}), description (150-160 chars about ${topic}), tags (5-8 ALL about ${topic}), readingTime (minutes)`,
        },
      ],
      temperature: 0.3,
      response_format: { type: "json_object" },
    });

    const metaData = JSON.parse(
      metaResponse.choices[0]?.message?.content || "{}",
    );
    const now = new Date().toISOString();

    return {
      title: metaData.title || topic,
      description:
        metaData.description || `Learn about ${topic} with expert insights`,
      keywords: [
        strategy.primary_keyword,
        ...strategy.secondary_keywords.slice(0, 4),
      ],
      author: "Agentica AI Team",
      category: "ai-insights",
      tags: metaData.tags || strategy.secondary_keywords.slice(0, 5),
      readingTime:
        metaData.readingTime || Math.ceil(content.split(" ").length / 200),
      publishDate: now,
      lastModified: now,
      schema: {
        type: "BlogPosting",
        headline: metaData.title || topic,
        description: metaData.description || `Learn about ${topic}`,
        datePublished: now,
        dateModified: now,
        author: {
          type: "Organization",
          name: "Agentica.it",
        },
      },
    };
  }

  /**
   * Optimize content with internal links
   */
  private async optimizeContent(
    content: string,
    metadata: BlogPostMetadata,
    language: string,
  ): Promise<string> {
    // Add internal links based on language
    const internalLinks: Record<
      string,
      Array<{ text: string; url: string }>
    > = {
      en: [
        { text: "AI agents", url: "/services/ai-agents" },
        { text: "automation solutions", url: "/services/automation" },
        { text: "custom AI development", url: "/services/custom-ai" },
        { text: "contact our team", url: "/contact" },
      ],
      es: [
        { text: "agentes de IA", url: "/es/services/ai-agents" },
        {
          text: "soluciones de automatización",
          url: "/es/services/automation",
        },
        {
          text: "desarrollo de IA personalizado",
          url: "/es/services/custom-ai",
        },
        { text: "contacta con nuestro equipo", url: "/es/contact" },
      ],
      it: [
        { text: "agenti AI", url: "/it/services/ai-agents" },
        { text: "soluzioni di automazione", url: "/it/services/automation" },
        { text: "sviluppo AI personalizzato", url: "/it/services/custom-ai" },
        { text: "contatta il nostro team", url: "/it/contact" },
      ],
    };

    let optimizedContent = content;
    const links = internalLinks[language] || internalLinks["en"];

    // Add internal links naturally
    links.forEach((link) => {
      const regex = new RegExp(`\\b${link.text}\\b`, "gi");
      let count = 0;
      optimizedContent = optimizedContent.replace(regex, (match) => {
        count++;
        // Only link first 2 occurrences
        return count <= 2 ? `[${match}](${link.url})` : match;
      });
    });

    // Add schema markup reference
    optimizedContent = `<!--
SEO Metadata:
Title: ${metadata.title}
Description: ${metadata.description}
Keywords: ${metadata.keywords.join(", ")}
Reading Time: ${metadata.readingTime} min
-->

${optimizedContent}

---

*Ready to transform your business with AI? [Contact Agentica.it](/${language}/contact) for a free consultation.*`;

    return optimizedContent;
  }

  /**
   * Generate featured image using DALL-E
   */
  private async generateFeaturedImage(
    topic: string,
    keyword: string,
  ): Promise<string> {
    if (!this.openai) throw new Error("OpenAI not initialized");

    try {
      const imagePrompt = `Modern, professional illustration for a blog post about "${topic}".
Style: Clean, minimalist tech design with gradients.
Colors: Blues, purples, and whites.
Elements: Abstract AI/automation symbols, geometric patterns.
Mood: Innovation, professionalism, future-forward.
Text overlay space at top for title.`;

      const response = await this.openai.images.generate({
        model: "dall-e-3",
        prompt: imagePrompt,
        n: 1,
        size: "1792x1024",
        quality: "hd",
        style: "vivid",
      });

      const imageUrl = response.data?.[0]?.url;
      if (!imageUrl) throw new Error("No image generated");

      // Download and save image
      const imageBuffer = await fetch(imageUrl).then((res) =>
        res.arrayBuffer(),
      );
      const imageName = `${keyword.toLowerCase().replace(/\s+/g, "-")}-${Date.now()}.png`;
      const imagePath = path.join(
        this.contentDir,
        "../../public/images/blog",
        imageName,
      );

      await fs.mkdir(path.dirname(imagePath), { recursive: true });
      await fs.writeFile(imagePath, Buffer.from(imageBuffer));

      return `/images/blog/${imageName}`;
    } catch (error) {
      console.error("Error generating image:", error);
      // Return a default image path
      return "/images/blog/default-ai-illustration.png";
    }
  }

  /**
   * Save blog post to file system
   */
  private async saveBlogPost(
    content: { metadata: BlogPostMetadata; content: string },
    language: string,
    category: string,
    imageUrl?: string,
  ): Promise<void> {
    const { metadata, content: blogContent } = content;

    // Add image to metadata if available
    if (imageUrl) {
      metadata.featuredImage = imageUrl;
      metadata.featuredImageAlt = `${metadata.title} - Agentica.it`;
      if (metadata.schema) {
        metadata.schema.image = imageUrl;
      }
    }

    // Create slug from title
    const slug = metadata.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "")
      .substring(0, 60);

    // Extract primary and secondary keywords
    const primaryKeyword = metadata.keywords[0] || slug;
    const secondaryKeywords = metadata.keywords.slice(1, 6); // Take next 5 keywords

    // Create frontmatter matching Astro schema
    const frontmatter = `---
title: "${metadata.title}"
metaTitle: "${metadata.title.substring(0, 60)}"
metaDescription: "${metadata.description}"
slug: "${slug}"
primaryKeyword: "${primaryKeyword}"
secondaryKeywords: [${secondaryKeywords.map((kw) => `"${kw}"`).join(", ")}]
publishDate: ${metadata.publishDate}
author: "${metadata.author}"
category: "${category}"
tags: [${metadata.tags.map((tag) => `"${tag}"`).join(", ")}]
readingTime: ${metadata.readingTime}
wordCount: ${blogContent.split(/\s+/).length}
language: "${language}"
featured: true
draft: false
${
  metadata.featuredImage
    ? `featuredImage:
  src: "${metadata.featuredImage}"
  alt: "${metadata.featuredImageAlt || metadata.title}"`
    : ""
}
---`;

    // Combine frontmatter and content
    const fullContent = `${frontmatter}\n\n${blogContent}`;

    // Create language-specific directory
    const langDir = path.join(this.contentDir, language);
    await fs.mkdir(langDir, { recursive: true });

    // Save the file
    const filename = `${slug}.md`;
    const filepath = path.join(langDir, filename);
    await fs.writeFile(filepath, fullContent, "utf-8");

    console.log(`✅ Blog post saved: ${filepath}`);
  }
}

// Export singleton instance with lazy initialization
let dataForSEOInstance: DataForSEO | null = null;

export const dataForSEO = {
  generateBlogPost: async (
    topic: string,
    language: "en" | "es" | "it" = "en",
    category: string = "ai-agents",
    researchLanguage?: "en" | "es" | "it",
    sharedImageUrl?: string,
    reviewKeywords?: boolean,
  ): Promise<string | undefined> => {
    if (!dataForSEOInstance) {
      dataForSEOInstance = new DataForSEO();
    }
    return dataForSEOInstance.generateBlogPost(
      topic,
      language,
      category,
      researchLanguage,
      sharedImageUrl,
      reviewKeywords,
    );
  },
};
