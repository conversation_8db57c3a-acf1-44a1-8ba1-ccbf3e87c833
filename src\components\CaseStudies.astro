---
import { getLangFromAstro, useTranslations } from '../i18n/utils';

const lang = getLangFromAstro(Astro);
const t = useTranslations(lang);
// Case Studies Section component
---

<section id="case-studies">
    <div class="container">
        <h2 class="section-title">{t("case_studies.title")}</h2>
        <p class="section-subtitle">{t("case_studies.subtitle")}</p>
        <div class="case-study-grid">
            <div class="case-study-card">
                <div>
                    <p class="category">{t("case_studies.card1_category")}</p>
                    <p class="metric">{t("case_studies.card1_metric")}</p>
                    <p class="metric-label">{t("case_studies.card1_metric_label")}</p>
                </div>
                <p class="testimonial">
                    "{t("case_studies.card1_testimonial")}"
                    <span class="testimonial-author">{t("case_studies.card1_author")}</span>
                </p>
            </div>
            <div class="case-study-card">
                <div>
                    <p class="category">{t("case_studies.card2_category")}</p>
                    <p class="metric">{t("case_studies.card2_metric")}</p>
                    <p class="metric-label">{t("case_studies.card2_metric_label")}</p>
                </div>
                <p class="testimonial">
                    "{t("case_studies.card2_testimonial")}"
                    <span class="testimonial-author">{t("case_studies.card2_author")}</span>
                </p>
            </div>
        </div>
    </div>
</section>
