---
import { getLangFromAstro, useTranslations, localizePath } from '../i18n/utils';
import { getCollection } from 'astro:content';

const lang = getLangFromAstro(Astro);
const t = useTranslations(lang);

// Get latest blog posts for the current language
const blogPosts = await getCollection('blog', ({ data }) => {
  return data.language === lang && data.draft !== true;
});

// Sort and get latest 3 posts
const latestPosts = blogPosts
  .sort((a, b) => b.data.publishDate.valueOf() - a.data.publishDate.valueOf())
  .slice(0, 3);
---

<footer class="footer">
    <div class="container">
        <!-- Main Footer Content -->
        <div class="footer-grid">
            <!-- Company Column -->
            <div class="footer-column">
                <h3>{t("footer.company")}</h3>
                <ul>
                    <li><a href={localizePath("/contact", lang)}>{t("footer.contact")}</a></li>
                    <li><a href={localizePath("/privacy-policy", lang)}>{t("footer.privacy")}</a></li>
                    <li><a href={localizePath("/terms-of-service", lang)}>{t("footer.terms")}</a></li>
                </ul>
            </div>

            <!-- Latest Blog Posts Column -->
            <div class="footer-column footer-blog">
                <h3>{t("footer.latestPosts")}</h3>
                <ul class="blog-list">
                    {latestPosts.map(post => (
                        <li class="blog-item">
                            <a href={localizePath(`/blog/${post.id}`, lang)}>
                                <span class="post-title">{post.data.title}</span>
                                <span class="post-date">
                                    {new Date(post.data.publishDate).toLocaleDateString(lang, {
                                        month: 'short',
                                        day: 'numeric'
                                    })}
                                </span>
                            </a>
                        </li>
                    ))}
                    {latestPosts.length === 0 && (
                        <li class="no-posts">{t("footer.noPosts")}</li>
                    )}
                </ul>
                <a href={localizePath("/blog", lang)} class="view-all-link">
                    {t("footer.viewAllPosts")}
                </a>
            </div>

            <!-- Logo and Social Column -->
            <div class="footer-column footer-brand">
                <div class="footer-logo-section">
                    <a href="https://proxy42.com/" target="_blank" rel="noopener noreferrer">
                        <img src="/images/logo_proxy.png" alt="Proxy Logo">
                    </a>
                </div>
                <div class="footer-social-section">
                    <a href="https://www.linkedin.com/company/proxy42" aria-label="LinkedIn" target="_blank">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                        </svg>
                    </a>
                    <a href="https://twitter.com/agentik_ai" aria-label="Twitter" target="_blank">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"/>
                        </svg>
                    </a>
                    
                </div>
            </div>
        </div>
        
    </div>

    <!-- Bottom Section -->
    <div class="container">
        <div class="footer-bottom-content">
            <div class="footer-address">
                <p>{t("footer.address")}</p>
            </div>
            <div class="footer-copyright">
                <p>{t("footer.copyright")}</p>
            </div>
        </div>
    </div>
</footer>

<style>
  .footer {
    background: #0a0a0f;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    padding: 60px 0 30px;
    margin-top: 100px;
    position: relative;
    overflow: hidden;
  }

  .footer .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    width: 100%;
  }

  .footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(139, 92, 246, 0.5) 25%,
      rgba(236, 72, 153, 0.5) 50%,
      rgba(139, 92, 246, 0.5) 75%,
      transparent 100%
    );
  }

    /* Main Grid */
    .footer-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      margin-bottom: 60px;
    }

    .footer-column h3 {
      font-size: 13px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 20px;
      color: rgba(255, 255, 255, 0.6);
    }

    .footer-column ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .footer-column li {
      margin-bottom: 12px;
    }

    .footer-column a {
      color: rgba(255, 255, 255, 0.6);
      text-decoration: none;
      font-size: 14px;
      transition: color 0.3s ease;
      display: block;
    }

    .footer-column a:hover {
      color: #ffffff;
    }

    /* Blog List Styles */
    .blog-list {
        display: flex;
        flex-direction: column;
        gap: 0;
    }

    .blog-item {
        margin: 0 0 12px 0;
        padding: 0;
        transition: all 0.3s ease;
    }

    .blog-item:last-child {
        margin-bottom: 0;
    }

    .blog-list a {
        display: block;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .blog-list a:hover .post-title {
        color: #8b5cf6;
    }

    .post-title {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        line-height: 1.4;
        font-weight: 400;
        transition: color 0.3s ease;
        display: block;
        margin-bottom: 2px;
    }

    .post-date {
        color: rgba(255, 255, 255, 0.4);
        font-size: 12px;
        font-weight: 400;
    }

    .no-posts {
      color: rgba(255, 255, 255, 0.3);
      font-style: italic;
      padding: 16px 0;
    }

    .view-all-link {
        display: inline-block;
        margin-top: 16px;
        color: #8b5cf6 !important;
        font-weight: 500;
        font-size: 14px !important;
        transition: all 0.3s ease;
        text-decoration: none;
        position: relative;
    }

    .view-all-link::after {
        content: '→';
        margin-left: 4px;
        transition: transform 0.3s ease;
        display: inline-block;
    }

    .view-all-link:hover {
        color: #a78bfa !important;
    }

    .view-all-link:hover::after {
        transform: translateX(3px);
    }

    /* Footer Brand Column */
    .footer-brand {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 24px;
    }

    .footer-logo-section img {
        height: 40px;
        opacity: 0.9;
        transition: opacity 0.3s ease;
    }

    .footer-logo-section:hover img {
        opacity: 1;
    }

    .footer-social-section {
        display: flex;
        gap: 16px;
    }

    .footer-social-section a {
        color: rgba(255, 255, 255, 0.6);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .footer-social-section a:hover {
        color: #ffffff;
        border-color: rgba(139, 92, 246, 0.6);
        transform: translateY(-2px);
    }

    /* Bottom Section */
    .footer-bottom {
      margin: 0 auto;
      position: relative;
    }

    .footer-bottom-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        width: 100%;
        text-align: center;
    }

    .footer-address p {
        margin: 0;
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.6);
        line-height: 1.6;
        letter-spacing: 0.3px;
    }

    .footer-copyright {
        text-align: center;
    }

    .footer-copyright p {
        color: rgba(255, 255, 255, 0.5);
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.5;
    }



    /* Responsive Design */
    @media (max-width: 1024px) {
        .footer-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
        }

        .footer .container {
            padding: 0 30px;
        }

        .footer-brand {
            grid-column: 1 / -1;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }

    @media (max-width: 768px) {
        .footer {
            padding: 60px 0 30px;
            margin-top: 80px;
        }

        .footer .container {
            padding: 0 20px;
        }

        .footer-grid {
            grid-template-columns: 1fr;
            gap: 48px;
        }

        .footer-column h3 {
            font-size: 13px;
            margin-bottom: 24px;
        }

        .blog-list a {
            padding: 12px 0;
        }

        .post-title {
            font-size: 0.875rem;
        }

        .view-all-link {
            margin-top: 16px;
        }

        .footer-bottom {
            padding: 50px 0 40px;
            margin-top: 50px;
        }

        .footer-bottom-content {
            gap: 20px;
        }

        .footer-brand {
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .footer-social-section {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .footer-column a {
            font-size: 14px;
        }

        .footer-column a::before {
            display: none;
        }

        .footer-column a:hover {
            transform: none;
        }

        .post-title {
            font-size: 14px;
        }

        .post-date {
            font-size: 12px;
        }

        .footer-social-section a {
            width: 40px;
            height: 40px;
        }
    }
</style>
