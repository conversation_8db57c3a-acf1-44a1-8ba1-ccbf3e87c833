name = "agentik-ai"
main = "dist/_worker.js"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]
account_id = "ab70a290d9d159f0efea694d4842da7e"

[assets]
directory = "dist"

# Database D1 per i contatti
[[d1_databases]]
binding = "DB"
database_name = "agentik-contacts"
database_id = "f429deb7-7ed9-4de9-bf9c-a057fdb09a16"

# KV Bindings per le sessioni
[[kv_namespaces]]
binding = "CACHE"
id = "d35dee28554e40adaefa4f05f6c8da8f"
preview_id = "f61ac2fee347443596cf7a03ba9e35dc"

[[kv_namespaces]]
binding = "SESSION"
id = "d35dee28554e40adaefa4f05f6c8da8f"
preview_id = "f61ac2fee347443596cf7a03ba9e35dc"

[env.production]
name = "agentik-ai"
# Uncomment routes when domain zones are configured in Cloudflare
# routes = [
#   { pattern = "agentik.ai", zone_name = "agentik.ai" },
#   { pattern = "www.agentik.ai", zone_name = "agentik.ai" }
# ]

# Database D1 per i contatti
[[env.production.d1_databases]]
binding = "DB"
database_name = "agentik-contacts"
database_id = "f429deb7-7ed9-4de9-bf9c-a057fdb09a16"

# KV Bindings per le sessioni
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "d35dee28554e40adaefa4f05f6c8da8f"
preview_id = "f61ac2fee347443596cf7a03ba9e35dc"

[[env.production.kv_namespaces]]
binding = "SESSION"
id = "d35dee28554e40adaefa4f05f6c8da8f"
preview_id = "f61ac2fee347443596cf7a03ba9e35dc"

[env.preview]
name = "agentik-ai-preview"
