---
import { getLang<PERSON>romAstro, useTranslations } from '../i18n/utils';

const lang = getLangFromAstro(Astro);
const t = useTranslations(lang);
// Problem Section component
---

<section id="problemi" class="problem-section">
    <div class="container" style="position: relative; z-index: 2;">
        <h2 class="section-title" set:html={t("problem.title")}></h2>
        <p class="section-subtitle">{t("problem.subtitle")}</p>
        <div class="problem-grid">
            <div class="problem-card">
                <div class="icon">
                 <svg xmlns="http://www.w3.org/2000/svg" id="Capa_2" data-name="Capa 2" viewBox="0 0 129.53 128.33">  
  <g id="Capa_1-2" data-name="Capa 1">
    <g>
      <path class="cls-2" d="m59.34,39.14c-.15-2.48-2.29-4.36-4.76-4.21-2.48.15-4.36,2.29-4.21,4.76v.14l1.52,21.26c.14,1.46,1.3,2.62,2.76,2.76h.28c.7-.01,1.38-.26,1.93-.69.59-.52.94-1.28.97-2.07l1.52-21.26v-.69Z"/>
      <path class="cls-2" d="m50.51,71.86c0,2.44,1.97,4.42,4.42,4.42s4.42-1.97,4.42-4.42c-.01-2.43-1.99-4.4-4.42-4.42-2.44,0-4.42,1.97-4.42,4.42Z"/>
      <path class="cls-2" d="m116.23,72.66l-9.07-4.21c-.04-.58-.11-1.16-.18-1.74h.01s-.03-.18-.03-.18c-.13-1.14-.28-2.29-.49-3.42l-2.12.52-11.47,2.13s0-.05-.01-.07l-3.67.75c.12.58.22,1.17.29,1.76,1.75,13.91-7.31,26.7-20.64,29.91v-.06s-3.46.64-3.46.64l2.55,13.72.67,3.6v.07s3.46-.64,3.46-.64v-.03c1.3-.28,2.6-.6,3.88-1l7.19,6.95c1.91,1.9,4.91,2.17,7.14.64l7.27-4.72c1.93-1.3,2.9-3.64,2.48-5.94-.06-.31-.13-.64-.24-.94l-3.42-9.33c2.34-2.68,4.34-5.65,5.94-8.83l9.94-.16c2.65-.07,4.9-1.94,5.48-4.53l1.79-8.48c.13-.71.14-1.44,0-2.16-.35-1.87-1.57-3.46-3.29-4.25Zm-2.03,14.12c-.21.99-1.04,1.72-2.05,1.79l-11.08.1c-.69-.01-1.33.39-1.64,1.01-1.72,3.53-3.95,6.79-6.63,9.66-.39.41-.56.99-.46,1.55.02.12.06.24.11.35l3.9,10.37c.04.09.07.2.08.29.16.84-.19,1.71-.86,2.24l-7.27,4.72c-.83.59-1.97.51-2.7-.2l-7.88-7.66c-.46-.45-1.15-.6-1.75-.38-1.49.51-3.01.91-4.55,1.24l-1.89-10.14c3.51-.81,6.86-2.23,9.89-4.23,5.21-3.41,9.24-8.32,11.61-14.07,1.87-4.49,2.63-9.34,2.29-14.16l10.07-1.87c.1.77.18,1.55.22,2.43-.02.13,0,.24.02.36.08.53.45.97.96,1.14l10.12,4.58c.68.31,1.16.94,1.27,1.67.03.25.05.5.03.74l-1.79,8.48Z"/>
      <path class="cls-2" d="m50.58,100.29c.54.06,1.09.1,1.63.14v.03s.5.02.5.02c.87.06,1.75.1,2.62.12v-.02s.39.01.39.01l.49-13.96h0v-.1s.11-3.24.11-3.24h-.24s0-.08,0-.08h-.39c-11.1.04-21.13-6.63-25.4-16.87-2.83-6.71-2.83-14.27,0-20.98,2.76-6.72,8.08-12.08,14.77-14.91,3.34-1.34,6.89-2.04,10.49-2.07.59-.01,1.19,0,1.78.04,14.23.84,25.27,12.46,25.65,26.45h-.08v3.51h17.67v-1.99c.04-2.08-.06-4.15-.29-6.21l8.14-5.8c2.22-1.53,3.04-4.43,1.93-6.9l-3.31-8.01c-.92-2.14-3.05-3.52-5.38-3.52-.32,0-.65.01-.97.07l-9.8,1.66c-2.21-2.79-4.76-5.3-7.59-7.45l1.66-9.8c.41-2.62-1.01-5.18-3.45-6.21l-8.01-3.31c-.68-.26-1.39-.4-2.13-.4-1.91,0-3.69.91-4.78,2.46l-5.8,8.14c-1.78-.19-3.58-.3-5.37-.3s-3.52.11-5.26.32l-5.8-8.14c-1.53-2.22-4.43-3.04-6.9-1.93l-8.01,3.31c-2.48,1.06-3.91,3.69-3.45,6.35l1.66,9.8c-2.79,2.21-5.3,4.76-7.45,7.59l-9.8-1.66c-.3-.04-.61-.07-.92-.07-2.32-.04-4.42,1.37-5.27,3.52l-3.31,8.01c-.97,2.5-.12,5.34,2.07,6.9l8.14,5.8c-.41,3.53-.41,7.1,0,10.63l-8.14,5.8c-2.22,1.53-3.04,4.43-1.93,6.9l3.31,8.01c.92,2.14,3.05,3.52,5.38,3.52.32,0,.65-.01.97-.07l9.8-1.66c2.21,2.79,4.76,5.3,7.59,7.45l-1.66,9.8c-.21,1.28.01,2.6.63,3.73.62,1.13,1.62,2.02,2.82,2.48l8.01,3.31c.7.28,1.45.41,2.21.41,1.88.01,3.63-.92,4.68-2.48l5.8-8.14Zm-8.43,5.94c-.43.61-1.13.97-1.88.94-.25,0-.5-.03-.73-.1l-8.01-3.31c-.94-.39-1.5-1.34-1.38-2.35l1.93-10.91c.14-.68-.14-1.38-.69-1.79-3.18-2.36-6-5.15-8.42-8.28-.32-.44-.84-.69-1.38-.69h-.28l-10.91,1.93c-.1.01-.21.01-.3.01-.86-.01-1.64-.51-2.04-1.27l-3.31-8.01c-.43-.92-.14-2.03.69-2.62l8.97-6.35c.54-.37.8-1.02.69-1.66-.55-3.89-.55-7.84,0-11.73.17-.65-.11-1.33-.69-1.66l-9.11-6.35c-.81-.61-1.15-1.66-.83-2.62l3.31-8.01c.33-.83,1.15-1.38,2.04-1.39.11,0,.21.01.3.03l10.91,1.93c.12.04.23.04.36.04.57-.01,1.09-.28,1.44-.73,2.36-3.18,5.15-6,8.28-8.42.55-.41.83-1.12.69-1.79l-1.93-10.91c-.11-.97.37-1.91,1.24-2.35l8.01-3.31c.29-.12.61-.19.92-.19.68-.01,1.31.32,1.7.88l6.35,8.97c.33.44.83.7,1.37.73.1,0,.19-.01.29-.04,3.89-.55,7.84-.55,11.73,0,.12.04.23.04.36.04.54.01,1.04-.26,1.3-.73l6.35-9.11c.43-.61,1.13-.97,1.88-.94.25.01.5.04.73.11l8.01,3.31c.94.39,1.5,1.34,1.38,2.35l-1.93,10.91c-.14.68.14,1.38.69,1.79,3.16,2.33,5.95,5.12,8.28,8.28.33.46.87.73,1.44.73.12,0,.25-.01.36-.04l10.91-1.93c.1-.03.21-.03.3-.03.86,0,1.64.5,2.04,1.26l3.31,8.01c.43.92.14,2.03-.69,2.62l-8.97,6.35c-.52.37-.8,1.02-.69,1.66.26,1.79.38,3.58.4,5.38h-10.3c-.07-3.83-.87-7.63-2.37-11.18-2.4-5.74-6.49-10.6-11.71-13.98-5.08-3.31-11.02-5.07-17.08-5.07-4.06,0-8.06.8-11.8,2.35-7.63,3.11-13.67,9.19-16.7,16.84-5.74,13.75-.83,29.62,11.66,37.73,4.23,2.75,9.06,4.42,14.06,4.9l-.36,10.21c-.73-.06-1.47-.14-2.19-.24-.12-.04-.23-.04-.36-.04-.54-.01-1.04.26-1.3.73l-6.35,9.11Z"/>
      <path class="cls-1" d="m114.93,52.83c.19.25.45.42.74.51.34.08.69,0,.98-.19l7.92-4.9.24-.18c.84-.69.96-1.93.27-2.76-.69-.84-1.93-.96-2.76-.27-.01.01-.03.02-.05.04l-7.14,5.98c-.48.42-.6,1.13-.27,1.68l.07.1Z"/>
      <path class="cls-1" d="m116.11,57.25l.03.12c.08.3.25.56.49.75.28.2.64.27.98.2l9.19-1.48.29-.07c1.04-.31,1.63-1.41,1.31-2.45-.31-1.04-1.41-1.63-2.45-1.31-.02,0-.04,0-.06.01l-8.89,2.77c-.61.2-.99.81-.9,1.45Z"/>
      <path class="cls-1" d="m127.73,60.62s-.04,0-.06,0l-9.29-.64c-.64-.03-1.21.4-1.36,1.03l-.02.12c-.04.31.03.61.18.88.19.29.5.48.84.54l9.1,1.95.3.04c1.08.09,2.03-.73,2.11-1.8.09-1.08-.73-2.03-1.8-2.11Z"/>
      <path class="cls-1" d="m62.17,115.85l-.12-.02c-.3-.04-.61.02-.88.16-.29.19-.49.49-.56.83l-2.13,9.06-.05.3c-.11,1.08.69,2.04,1.76,2.15,1.08.11,2.04-.69,2.15-1.76,0-.02,0-.04,0-.06l.82-9.27c.04-.64-.38-1.22-1-1.38Z"/>
      <path class="cls-1" d="m58.08,113.55l-.1-.06c-.26-.16-.57-.22-.88-.19-.34.06-.64.26-.84.55l-5.46,7.54-.16.26c-.52.96-.15,2.15.8,2.66.96.52,2.15.15,2.66-.8,0-.02.02-.04.03-.05l4.33-8.24c.29-.58.12-1.27-.39-1.66Z"/>
      <path class="cls-1" d="m54.8,111.54l-.07-.1c-.19-.24-.45-.41-.75-.49-.34-.07-.69,0-.98.21l-7.82,5.06-.24.18c-.83.71-.92,1.95-.22,2.77.71.83,1.95.92,2.77.22.01-.01.03-.03.05-.04l7.02-6.12c.47-.43.57-1.14.24-1.69Z"/>
    </g>
  </g>
</svg>
                </div>
                <h3>{t("problem.card1_title")}</h3>
                <p>{t("problem.card1_desc")}</p>
            </div>

            <div class="problem-card">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 84.064 81.906" fill="currentColor">
                        <g transform="translate(-7.968 -9.047)">
                            <path d="M39.156,27.3c-15.016,0-31.188-2.859-31.188-9.125S24.124,9.047,39.156,9.047s31.188,2.859,31.188,9.125S54.188,27.3,39.156,27.3Zm0-15.125c-18.156,0-28.062,3.969-28.062,6s9.906,6,28.062,6,28.062-3.969,28.062-6-9.906-6-28.062-6Z"/>
                            <path d="M39.156,42.406c-15.016,0-31.188-2.859-31.188-9.125V18.156a1.562,1.562,0,1,1,3.125,0V33.281c0,2.031,9.906,6,28.062,6s28.062-3.969,28.062-6V18.156a1.563,1.563,0,0,1,3.125,0V33.281c0,6.266-16.156,9.125-31.188,9.125Z"/>
                            <path d="M68.766,48.922A1.567,1.567,0,0,1,67.2,47.36v-14a1.562,1.562,0,0,1,3.125,0v14A1.567,1.567,0,0,1,68.766,48.922Z"/>
                            <path d="M39.156,57.594c-15.016,0-31.188-2.859-31.188-9.125V33.36a1.562,1.562,0,1,1,3.125,0V48.469c0,2.031,9.906,6,28.062,6a104,104,0,0,0,13.75-.875,1.56,1.56,0,1,1,.406,3.094,107.284,107.284,0,0,1-14.156.906Z"/>
                            <path d="M39.156,72.812c-15.016,0-31.188-2.844-31.188-9.109V48.578a1.562,1.562,0,1,1,3.125,0V63.7c0,2.031,9.906,5.984,28.062,5.984,3.141,0,6.266-.125,9.281-.391a1.56,1.56,0,1,1,.266,3.109c-3.094.266-6.312.391-9.531.391Z"/>
                            <path d="M39.156,88.125C24.14,88.125,7.968,85.266,7.968,79V63.875a1.562,1.562,0,1,1,3.125,0V79c0,2.031,9.906,6,28.062,6A96.166,96.166,0,0,0,56.4,83.563a1.566,1.566,0,0,1,.578,3.078,98.244,98.244,0,0,1-17.828,1.5Z"/>
                            <path d="M69.438,90.953A22.578,22.578,0,1,1,92.032,68.375,22.6,22.6,0,0,1,69.438,90.953Zm0-42.047A19.453,19.453,0,1,0,88.907,68.359,19.475,19.475,0,0,0,69.438,48.906Z"/>
                            <path d="M69.438,72.344a1.567,1.567,0,0,1-1.562-1.562V54.063a1.563,1.563,0,0,1,3.125,0V70.782A1.567,1.567,0,0,1,69.438,72.344Z"/>
                            <path d="M69.453,82.016a1.567,1.567,0,0,1-1.562-1.562V77.188a1.563,1.563,0,0,1,3.125,0v3.266A1.567,1.567,0,0,1,69.453,82.016Z"/>
                        </g>
                    </svg>
                </div>
                <h3>{t("problem.card2_title")}</h3>
                <p>{t("problem.card2_desc")}</p>
            </div>

            <div class="problem-card">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 55.903 56" fill="currentColor">
                        <g transform="translate(-4 -4)">
                            <g>
                                <path d="M54.5,20.6l5.2-8.4V12c0-.1.1-.2.1-.3h0V5a1,1,0,0,0-1-1H5A.945.945,0,0,0,4,5v6.6H4a.367.367,0,0,0,.1.3v.2L22.3,41.8v3.5c.033.033,0,1.9,0,1.9V59a1.02,1.02,0,0,0,.5.8.551.551,0,0,0,.5.2.6.6,0,0,0,.4-.1l17.1-8.4a1.05,1.05,0,0,0,.5-.9V43.2a12.97,12.97,0,0,0,5.5,1.2A13.145,13.145,0,0,0,59.9,31.3,12.775,12.775,0,0,0,54.5,20.6ZM57.9,5.9v4.7H5.9V5.9ZM24.1,41,6.7,12.6H57.1l-4.3,7h-.1a6.117,6.117,0,0,0-1.2-.5c-.1,0-.1,0-.2-.1a4.951,4.951,0,0,0-1.3-.4c-.1,0-.2,0-.2-.1a5.853,5.853,0,0,0-1.3-.2h-.2c-.5,0-.9-.1-1.4-.1A13.145,13.145,0,0,0,33.8,31.3a9.7,9.7,0,0,0,.1,1.7,4.331,4.331,0,0,0,.1.5c.1.4.1.7.2,1.1.1.2.1.4.2.6l.3.9.3.6a5.937,5.937,0,0,0,.4.8,2.092,2.092,0,0,0,.4.6c.2.3.3.5.5.8.1.2.3.4.4.6.2.2.4.5.6.7l.5.5a4.349,4.349,0,0,0,.7.6c.2.2.4.3.6.5.2.1.3.3.5.4v3.2H24.3V41.7A1.165,1.165,0,0,0,24.1,41Zm.2,16.5V47.2H39.5V50ZM46.9,42.4a11.2,11.2,0,1,1,4.4-21.5,5.388,5.388,0,0,1,1.3.7,11.212,11.212,0,0,1-5.7,20.8Z"/>
                                <path d="M51.7,26.4a.967.967,0,0,0-1.4,0l-3.5,3.5-3.5-3.5a.99.99,0,0,0-1.4,1.4l3.5,3.5L42,34.7a.967.967,0,0,0,0,1.4.967.967,0,0,0,1.4,0l3.5-3.5,3.5,3.5a.967.967,0,0,0,1.4,0,.967.967,0,0,0,0-1.4l-3.5-3.5,3.5-3.5A.946.946,0,0,0,51.7,26.4Z"/>
                            </g>
                        </g>
                    </svg>
                </div>
                <h3>{t("problem.card3_title")}</h3>
                <p>{t("problem.card3_desc")}</p>
            </div>
        </div>
    </div>
</section>
