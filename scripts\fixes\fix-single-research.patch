--- a/agentica.it/src/utils/dataforseo.ts
+++ b/agentica.it/src/utils/dataforseo.ts
@@ -119,6 +119,7 @@ class DataForSEO {
   private config: DataForSEOConfig;
   private contentDir: string;
+  private cachedKeywordStrategy: { topic: string; language: string; strategy: ContentStrategy } | null = null;

   constructor() {
     this.authFetch = this.createAuthenticatedFetch();
@@ -265,11 +266,25 @@ class DataForSEO {
   private async performKeywordResearch(
     topic: string,
     language: string,
     reviewKeywords: boolean = false,
+    isUsingSharedResearch: boolean = false,
   ): Promise<ContentStrategy> {
     if (!this.labsApi) throw new Error("Labs API not initialized");

+    // Check if we have a cached strategy for this topic when using shared research
+    if (isUsingSharedResearch && this.cachedKeywordStrategy) {
+      if (this.cachedKeywordStrategy.topic === topic) {
+        console.log(chalk.cyan(`📊 Using cached ${this.cachedKeywordStrategy.language} keyword research for ${language} content`));
+        return this.cachedKeywordStrategy.strategy;
+      }
+    }
+
     const locationCode = this.getLocationCode(language);
+
+    // If using shared research and this is not the research language, don't force review
+    const shouldReviewKeywords = isUsingSharedResearch
+      ? false  // Never review keywords when using shared research
+      : reviewKeywords;

     // Create keyword ideas request
     const keywordIdeasRequest =
@@ -315,7 +330,15 @@ class DataForSEO {
     );

     // Build content strategy with interactive selection
-    return await this.buildContentStrategy(topic, keywordData, reviewKeywords);
+    const strategy = await this.buildContentStrategy(topic, keywordData, shouldReviewKeywords);
+
+    // Cache the strategy if this is the primary research language
+    if (isUsingSharedResearch && !this.cachedKeywordStrategy) {
+      this.cachedKeywordStrategy = { topic, language, strategy };
+    }
+
+    return strategy;
   }

   /**
@@ -199,6 +199,7 @@ class DataForSEO {
     researchLanguage?: "en" | "es" | "it",
     sharedImageUrl?: string,
     reviewKeywords: boolean = false,
+    clearCache: boolean = true,
   ): Promise<string | undefined> {
     await this.initializeClients();

+    // Clear keyword cache at the start of a new generation session
+    if (clearCache) {
+      this.cachedKeywordStrategy = null;
+    }
+
+    const isUsingSharedResearch = researchLanguage && researchLanguage !== language;
+
     try {
       // Step 1: Perform comprehensive keyword research
       console.log(`🔍 Performing keyword research for: ${topic}`);
       const keywordStrategy = await this.performKeywordResearch(
         topic,
         researchLanguage || language,
-        reviewKeywords,
+        reviewKeywords && !isUsingSharedResearch, // Only review if not using shared research
+        isUsingSharedResearch,
       );

       // Step 2: Analyze top competitors
--- a/agentica.it/scripts/generate-blog-post.ts
+++ b/agentica.it/scripts/generate-blog-post.ts
@@ -388,12 +388,15 @@ async function main() {
     // Track shared image URL across languages
     let sharedImageUrl: string | undefined;

-    for (const lang of languages) {
+    for (let i = 0; i < languages.length; i++) {
+      const lang = languages[i];
+      const isFirstLanguage = i === 0;
+
       const imageUrl = await generateBlogPost(
         topic,
         lang,
         category,
         options.dryRun,
         researchLang,
         sharedImageUrl,
-        options.reviewKeywords || options.interactive,
+        (options.reviewKeywords || options.interactive) && (!researchLang || isFirstLanguage),
       );

       // Use the image from the first language for all subsequent ones
@@ -244,6 +244,9 @@ async function generateBlogPost(
     spinner.text = `Humanizing and optimizing content for "${topic}" (${language})...`;
     await new Promise((resolve) => setTimeout(resolve, 1000));

+    // Determine if we should clear the cache (only clear on first language)
+    const shouldClearCache = !sharedImageUrl; // If no shared image, this is the first run
+
     // Generate the blog post
     const imageUrl = await dataForSEO.generateBlogPost(
       topic,
@@ -252,6 +255,7 @@ async function generateBlogPost(
       researchLanguage,
       sharedImageUrl,
       reviewKeywords,
+      shouldClearCache,
     );

     spinner.succeed(
